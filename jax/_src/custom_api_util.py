# Copyright 2022 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


_custom_wrapper_types = set()

def register_custom_decorator_type(cls):
  _custom_wrapper_types.add(cls)
  return cls

def forward_attr(self_, name):
  if name.startswith('def') and type(self_.fun) in _custom_wrapper_types:
    return getattr(self_.fun, name)
  else:
    raise AttributeError
