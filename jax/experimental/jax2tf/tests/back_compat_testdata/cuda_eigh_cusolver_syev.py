# Copyright 2023 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# flake8: noqa

import datetime
from numpy import array, float32

data_2023_03_17=dict(
    # Pasted from the test output (see back_compat_test.py module docstring)
    f32_syevj=dict(
        testdata_version=1,
        platform='cuda',
        custom_call_targets=['cusolver_syevj'],
        serialized_date=datetime.date(2023, 3, 17),
        inputs=(),
        expected_outputs=(array([[ 6.18577063e-01, -8.00570633e-05, -1.96905047e-01,
            -8.95753130e-02,  7.24549413e-01, -1.07546024e-01,
            -4.77200520e-04,  1.84469908e-01],
          [ 4.70708847e-01,  3.31519186e-05,  2.80930042e-01,
            -5.84393919e-01, -4.93098050e-01, -2.50211239e-01,
            -1.14346610e-03,  2.28566617e-01],
          [ 3.22840720e-01, -5.11042356e-01, -3.03526163e-01,
            2.48800799e-01, -3.14544559e-01,  5.54342926e-01,
            1.10838346e-06,  2.72663534e-01],
          [ 1.74972475e-01,  4.18093473e-01, -2.66933769e-01,
            5.78716159e-01, -2.97307134e-01, -4.46864694e-01,
            1.09066934e-06,  3.16760242e-01],
          [ 2.71042082e-02,  4.29418474e-01,  4.71952170e-01,
            1.10573582e-01,  9.57800150e-02,  4.65731144e-01,
            -4.72866714e-01,  3.60856950e-01],
          [-1.20763958e-01, -3.84347916e-01,  5.79687178e-01,
            2.87678182e-01,  1.63329691e-01, -2.02215970e-01,
            4.32829827e-01,  4.04953718e-01],
          [-2.68632114e-01,  3.63640338e-01, -2.97110289e-01,
            -3.32554609e-01,  3.46945561e-02,  2.77071655e-01,
            5.63131213e-01,  4.49050426e-01],
          [-4.16500419e-01, -3.15715015e-01, -2.68094122e-01,
            -2.19244853e-01,  8.65960941e-02, -2.90307850e-01,
            -5.21475971e-01,  4.93147314e-01]], dtype=float32), array([-2.4598812e+01, -2.4345848e-06, -1.2664314e-06, -8.6959182e-07,
          -8.2917722e-07,  1.6633214e-06,  2.0499781e-06,  2.7659885e+02],
          dtype=float32)),
        mlir_module_text="""
module @jit__lambda_ {
  func.func public @main() -> (tensor<8x8xf32> {jax.result_info = "[0]"}, tensor<8xf32> {jax.result_info = "[1]"}) {
    %0 = stablehlo.iota dim = 0 : tensor<64xf32>
    %1 = stablehlo.reshape %0 : (tensor<64xf32>) -> tensor<8x8xf32>
    %2 = stablehlo.transpose %1, dims = [1, 0] : (tensor<8x8xf32>) -> tensor<8x8xf32>
    %3 = stablehlo.add %1, %2 : tensor<8x8xf32>
    %4 = stablehlo.constant dense<2.000000e+00> : tensor<f32>
    %5 = stablehlo.broadcast_in_dim %4, dims = [] : (tensor<f32>) -> tensor<8x8xf32>
    %6 = stablehlo.divide %3, %5 : tensor<8x8xf32>
    %7 = call @tril(%6) : (tensor<8x8xf32>) -> tensor<8x8xf32>
    %8 = stablehlo.custom_call @cusolver_syevj(%7) {api_version = 2 : i32, backend_config = "\00\00\00\00\00\00\00\00\01\00\00\00\08\00\00\00M\08\00\00", operand_layouts = [dense<[0, 1]> : tensor<2xindex>], output_operand_aliases = [#stablehlo.output_operand_alias<output_tuple_indices = [0], operand_index = 0, operand_tuple_indices = []>], result_layouts = [dense<[0, 1]> : tensor<2xindex>, dense<0> : tensor<1xindex>, dense<> : tensor<0xindex>, dense<0> : tensor<1xindex>]} : (tensor<8x8xf32>) -> tuple<tensor<8x8xf32>, tensor<8xf32>, tensor<i32>, tensor<2125xf32>>
    %9 = stablehlo.get_tuple_element %8[0] : (tuple<tensor<8x8xf32>, tensor<8xf32>, tensor<i32>, tensor<2125xf32>>) -> tensor<8x8xf32>
    %10 = stablehlo.get_tuple_element %8[1] : (tuple<tensor<8x8xf32>, tensor<8xf32>, tensor<i32>, tensor<2125xf32>>) -> tensor<8xf32>
    %11 = stablehlo.get_tuple_element %8[2] : (tuple<tensor<8x8xf32>, tensor<8xf32>, tensor<i32>, tensor<2125xf32>>) -> tensor<i32>
    %12 = stablehlo.get_tuple_element %8[3] : (tuple<tensor<8x8xf32>, tensor<8xf32>, tensor<i32>, tensor<2125xf32>>) -> tensor<2125xf32>
    %13 = stablehlo.constant dense<0> : tensor<i32>
    %14 = stablehlo.broadcast_in_dim %13, dims = [] : (tensor<i32>) -> tensor<i32>
    %15 = stablehlo.compare  EQ, %11, %14,  SIGNED : (tensor<i32>, tensor<i32>) -> tensor<i1>
    %16 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1x1xi1>
    %17 = stablehlo.constant dense<0x7FC00000> : tensor<f32>
    %18 = stablehlo.broadcast_in_dim %17, dims = [] : (tensor<f32>) -> tensor<8x8xf32>
    %19 = stablehlo.broadcast_in_dim %16, dims = [0, 1] : (tensor<1x1xi1>) -> tensor<8x8xi1>
    %20 = stablehlo.select %19, %9, %18 : tensor<8x8xi1>, tensor<8x8xf32>
    %21 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1xi1>
    %22 = stablehlo.constant dense<0x7FC00000> : tensor<f32>
    %23 = stablehlo.broadcast_in_dim %22, dims = [] : (tensor<f32>) -> tensor<8xf32>
    %24 = stablehlo.broadcast_in_dim %21, dims = [0] : (tensor<1xi1>) -> tensor<8xi1>
    %25 = stablehlo.select %24, %10, %23 : tensor<8xi1>, tensor<8xf32>
    return %20, %25 : tensor<8x8xf32>, tensor<8xf32>
  }
  func.func private @tril(%arg0: tensor<8x8xf32>) -> tensor<8x8xf32> {
    %0 = stablehlo.iota dim = 0 : tensor<8x8xi32>
    %1 = stablehlo.constant dense<0> : tensor<i32>
    %2 = stablehlo.broadcast_in_dim %1, dims = [] : (tensor<i32>) -> tensor<8x8xi32>
    %3 = stablehlo.add %0, %2 : tensor<8x8xi32>
    %4 = stablehlo.iota dim = 1 : tensor<8x8xi32>
    %5 = stablehlo.compare  GE, %3, %4,  SIGNED : (tensor<8x8xi32>, tensor<8x8xi32>) -> tensor<8x8xi1>
    %6 = stablehlo.constant dense<0.000000e+00> : tensor<f32>
    %7 = stablehlo.broadcast_in_dim %6, dims = [] : (tensor<f32>) -> tensor<8x8xf32>
    %8 = stablehlo.select %5, %arg0, %7 : tensor<8x8xi1>, tensor<8x8xf32>
    return %8 : tensor<8x8xf32>
  }
}
""",
        mlir_module_serialized=b"ML\xefR\x03MLIRxxx-trunk\x00\x01-\x05\x01\x05\x01\x03\x05\x03\x1d\x07\t\x0b\r\x0f\x11\x13\x15\x17\x19\x1b\x1d\x1f!\x03^\x02\xeb5\x01\x95\x0f\x17\x13\x07\x0f\x0b\x0b\x0b\x0b\x0b\x17\x0b\x0b\x0b\x0b\x13\x0b\x13\x0f\x0b\x0b\x17\x0f\x13\x13\x0b33\x0b\x0f\x0b\x0b\x13\x0f\x0b\x1b\x0f\x0b\x13\x0f\x0b\x0f\x0b\x0f\x0b\x0f\x0b\x13\x0b\x0f\x0b\x0f\x0b\x13\x0b\x13\x0bK\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x13\x13\x13\x13\x1b\x13\x13\x03W\x0b\x0b\x0f\x0b\x0bO/\x0b\x13\x13\x0b\x13\x0b\x0b\x0b\x0b\x0b\x0b\x0f\x1f\x0f\x0f\x0b\x1fO\x1f\x0b\x0b\x0b\x0b\x0f\x0f\x17\x1b\x0f\x0f\x0f\x0f\x0f\x0b\x1fO/\x035\x17\x0f\x07\x0f\x07\x13\x07\x07\x17\x07\x17\x13\x17\x17\x17\x13\x17\x1b\x13\x13\x13\x0f\x17\x13\x13\x13\x02r\x08\x1d\x85\x03\x17\x116\x04\x01\x03\x03\x13\xbd\x1f\x1d9\x03\x05#\x05%\x05'\x05)\x05+\x17\x112\x04\x01\x05-\x05/\x051\x053\x03\x03!\xb9\x055\x03\x03\x0b\xbb\x1d?\x03\x057\x059\x17\x11*\x04\x01\x1dm\x15\x03\x03\x0b\xe5\x03\x03\x0f3\x05;\x03\x0b\x17\x95\x19\xa3\x1b\xa5\x0f\xaf\x1d\xb1\x03\x0b\x17\x99\x19\xb5\x1b\x99\x0f\x9b\x1d\xb7\x05=\x1d=\x03\x05?\x05A\x03\x03!\xbf\x1dE\x03\x05C\x03\x05'\x9d)\xc1\x1dK\x03\x05E\x03\x03\x0b\xc3\x1dQ\x03\x05G\x1dU\x03\x05I\x1dY+\x05K\x1d]+\x05M\x03\x03a\xc5\x05O\x1de\x15\x05Q\x1di\x15\x05S\x03\x03\x0b\xc7\x05U\x03\x03q\x9b\x05W\x03\x11u\xc9w\xcby\xcd{\x95}\xcf\x7f\xd1\x81\xd3\x83\xd7\x05Y\x05[\x05]\x05_\x05a\x05c\x05e\x05g\x05i\x03\x03\r\xdb\x03\x03\r\xdd\x03\x03\r\xdf\x03\x03\r\xe1\x03\x05'\x9d)\xe3\x03\x03\x13\xe7\x03\x03\x13\xe9\x03\x01\x1dk\x03\x03\xb3\x1dm\t\x07\x1f%!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f'\x11\x00\x00\x00\x00\x00\x00\x00\x00#\x1b\x03\x05\xa7\xab\r\x03\x97\xa9\x1do\r\x03\x97\xad\x1dq\x1ds\x1du\r\x01#\x1d\x1dw\x13\r\x01\x1f\x07\t\x00\x00\x00\x00\x1f\x1f\x01\x13\r\x05\x07\x05\x1f\x03\t\x00\x00\x00\x00\x1f\x17!\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x03\t\x00\x00\x00@\x0b\x05\x1dy\x1d{\x05\x01\x03\x03\x9f\x03\x03\xd5\x15\x03\x01\x01\x01\x03\t\x9f\xa1\xd9\xa1\x1f)\x01\x13\x05\x01\x13\x05\x05\x13\x05\t\x13\x05\r\x07\x01\x1f\x03\t\x00\x00\xc0\x7f\x1f\x17!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f3\x11\x00\x00\x00\x00\x00\x00\x00\x00)\x05!!\t)\x01\t\x1b)\x01\x05\t)\x03!\t\x1d\x01)\x05!!\x05\x13)\x05!!\x0f)\x03\t\r)\x03jB\t\x11\x01\x05\x01\x0b\x11\x03\x01\x03\x01)\x03\x01\r)\x03\x02\x02\t/\t\x01\x0b\x07\x19)\x03\t\x13)\x03\x05\x13)\x03\x01\x13)\x01\x0f)\x05\x05\x05\x0f)\x03\x05\x0f)\x03!\x0f)\x03\x05\r\x04\xc6\x04\x05\x01\x11\x071\x07\x03\x01\t\r\x11\x075\x05\x035m\t\x03W\x1f\x03!\x15\x06[\x03\x01\x03\x01\x17\x07c_\x03\x01\x03\x03\x0f\x06g\x03\x01\x05\x03\x05\x05\x03\x07k\x03\x03\x03\x07-\x05\x03\x01\x03\t\x19\x06-\x03\x01\x05\x07\x0b\x1b\x07\to\x03\x01\x03\r\x1d\x07\x01s\x03#\x03\x0f\x07\x07\x01\x87\x03\x01\x03\x11\x07\x07\x01\x89\x03\x0b\x03\x11\x07\x07\x01\x8b\x03\x07\x03\x11\x07\x07\x01\x8d\x03\x19\x03\x11\x05\x03\x01#\x03\x07\x03\x07\x01\x05\x03\x07\x03\x1b\x11\x07\x01\x8f\x03+\x05\x17\x1d\x03\x07\x01\x05\x03-\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x01\x03#\x03\x07\x01\x91\x03\x15\x03!\x0b\x06\x01\x03\x01\x07'\x13%\x03\x07\x01\x05\x03/\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x0b\x03-\x03\x07\x01\x93\x031\x03+\x0b\x06\x01\x03\x0b\x071\x15/\x13\x04\x07\x05)3\r\x11\t7\x05\x03\x15+\x03\x01\x07\t\x03;\x1f\x03\x11\x05\x03\t#\x03\x07\x03\x07%\x05\x03\x11\x03\x05\x0f\x06%\x03\x11\x05\x03\x07\t\x03CA\x03\x11\x11\x07IG\x03\x15\x05\t\x0b\x05\x03\tM\x03\x03\x03\x07O\x05\x03\x01\x03\x0f\x0b\x06S\x03\x01\x07\r\x01\x11\x13\x04\t\x03\x13\x06\x03\x01\x05\x01\x00\x06\x1a}\x1f+\x11\x0f\x0b\t\t\x0b!\x7f\x1f/!!)#\x1f\x19\x0f99m\x19\x85\x89W\xb3K\x9bM\x9b\x96\x04\x1b+\x1b\x1f\x1f\x15\x1d\x15+\x83\x13\r\r\x1f\x11\x15\x1b\x17\x15\x17\x0f\x11\x15\x11+\x19)\x0f\x0b\x11builtin\x00vhlo\x00module\x00broadcast_in_dim_v1\x00constant_v1\x00get_tuple_element_v1\x00iota_v1\x00select_v1\x00func_v1\x00add_v1\x00compare_v1\x00return_v1\x00reshape_v1\x00transpose_v1\x00divide_v1\x00call_v1\x00custom_call_v1\x00value\x00index\x00sym_name\x00third_party/py/jax/experimental/jax2tf/tests/back_compat_test.py\x00broadcast_dimensions\x00arg_attrs\x00function_type\x00res_attrs\x00sym_visibility\x00iota_dimension\x00compare_type\x00comparison_direction\x00jit__lambda_\x00jit(<lambda>)/jit(main)/pjit[in_shardings=(UnspecifiedValue,) out_shardings=(UnspecifiedValue,) resource_env=None donated_invars=(False,) name=tril in_positional_semantics=(<_PositionalSemantics.GLOBAL: 1>,) out_positional_semantics=_PositionalSemantics.GLOBAL keep_unused=False inline=False]\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(8, 8) dimension=0]\x00jit(<lambda>)/jit(main)/jit(tril)/add\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(8, 8) dimension=1]\x00jit(<lambda>)/jit(main)/jit(tril)/ge\x00jit(<lambda>)/jit(main)/jit(tril)/broadcast_in_dim[shape=(8, 8) broadcast_dimensions=()]\x00jit(<lambda>)/jit(main)/jit(tril)/select_n\x00jit(<lambda>)/jit(main)/iota[dtype=float32 shape=(64,) dimension=0]\x00jit(<lambda>)/jit(main)/reshape[new_sizes=(8, 8) dimensions=None]\x00permutation\x00jit(<lambda>)/jit(main)/transpose[permutation=(1, 0)]\x00jit(<lambda>)/jit(main)/add\x00jit(<lambda>)/jit(main)/div\x00callee\x00api_version\x00backend_config\x00call_target_name\x00called_computations\x00has_side_effect\x00operand_layouts\x00output_operand_aliases\x00result_layouts\x00jit(<lambda>)/jit(main)/eigh[lower=True sort_eigenvalues=True]\x00jax.result_info\x00tril\x00[0]\x00[1]\x00main\x00public\x00private\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00M\x08\x00\x00\x00cusolver_syevj\x00",
        xla_call_module_version=4,
    ),  # End paste

    # Pasted from the test output (see back_compat_test.py module docstring)
    f32_syevd=dict(
        testdata_version=1,
        platform='cuda',
        custom_call_targets=['cusolver_syevd'],
        serialized_date=datetime.date(2023, 3, 17),
        inputs=(),
        expected_outputs=(array([[ 3.14863890e-01,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            -4.91220355e-01,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  0.00000000e+00,
            0.00000000e+00,  0.00000000e+00,  8.05416584e-01,
            0.00000000e+00, -1.77893345e-03, -2.64500137e-02,
            1.46598322e-04, -5.19353598e-02, -8.64148438e-02],
          [ 2.99391806e-01,  2.77544819e-02,  6.73292065e-03,
            -6.83086272e-03, -3.54272849e-03, -1.21014733e-02,
            -1.32716037e-02, -1.15843862e-03, -8.83520208e-03,
            -6.63395738e-03,  1.60171092e-03, -1.01765711e-03,
            1.19860061e-02, -1.33239310e-02,  1.76237477e-03,
            1.27085261e-02,  3.38556734e-03, -8.78101215e-03,
            1.58616400e-03, -7.37631368e-03,  3.81911686e-03,
            -5.18379211e-02, -7.22059654e-03,  1.85085051e-02,
            2.94725411e-03,  4.74284729e-03, -1.33781182e-02,
            -3.61499190e-03, -5.49228955e-03, -1.05845921e-01,
            1.01772454e-02,  4.47412670e-01,  1.95654288e-01,
            3.94686669e-01,  7.00925171e-01, -9.06614065e-02],
          [ 2.83920437e-01,  1.69272088e-02,  6.64264262e-02,
            -1.18565477e-01,  3.54601629e-02, -1.52457461e-01,
            6.84847543e-03,  1.90414500e-03, -2.76310533e-01,
            3.76881436e-02,  1.22269124e-01, -1.01556584e-01,
            -1.90264836e-01, -1.16590485e-01,  6.09031200e-01,
            -9.43092555e-02, -3.74726858e-03, -2.33182713e-01,
            1.95203945e-01, -1.20613754e-01,  3.94887812e-02,
            -5.88066364e-03,  1.19152360e-01, -1.46030456e-01,
            -4.74781469e-02,  2.67041594e-01, -1.22617789e-01,
            5.77996820e-02,  2.58437768e-02, -1.34434626e-01,
            -3.28330845e-02, -9.32494774e-02,  1.14714004e-01,
            1.21207587e-01, -2.04871535e-01, -9.49072391e-02],
          [ 2.68448830e-01,  2.17946004e-02, -1.94895901e-02,
            3.40374447e-02,  6.18659109e-02,  1.72068894e-01,
            -8.02555401e-03,  9.68076065e-02,  4.98391055e-02,
            5.55528253e-02, -3.23998183e-02, -2.63249427e-01,
            -4.35045222e-03,  5.20016700e-02, -5.92328422e-02,
            4.31317724e-02, -2.00986061e-02, -2.69871447e-02,
            1.54309347e-01,  1.74670279e-01, -4.97168908e-03,
            -4.15510803e-01, -4.33471389e-02, -3.71299796e-02,
            5.26434295e-02, -1.18867345e-01, -2.42547281e-02,
            -3.90263759e-02, -2.58720964e-01, -3.92957211e-01,
            -1.28192365e-01,  2.77028710e-01, -4.02157485e-01,
            -1.77024350e-01, -1.76668167e-01, -9.91534367e-02],
          [ 2.52977222e-01,  3.48518007e-02,  7.02044442e-02,
            1.42712081e-02,  4.50692251e-02,  7.16193160e-03,
            1.19931757e-01,  2.32399218e-02, -6.05047755e-02,
            1.06077030e-01,  1.03731848e-01, -1.13200452e-02,
            5.94755262e-03, -2.32813850e-01,  8.72232541e-02,
            8.17264095e-02,  3.30835059e-02,  4.88227099e-01,
            6.14454560e-02,  1.43805355e-01, -7.40422234e-02,
            2.25823849e-01, -3.86487693e-01,  1.30468249e-01,
            3.16427708e-01, -1.19733319e-01, -4.18486483e-02,
            -2.74667948e-01, -2.16731444e-01,  2.60375626e-02,
            5.77645637e-02, -7.56322592e-02,  2.28632554e-01,
            2.37157010e-02, -1.40153974e-01, -1.03399649e-01],
          [ 2.37505659e-01,  7.01064467e-02, -3.83728333e-02,
            5.06979637e-02,  1.83892641e-02,  4.02548499e-02,
            -3.88330072e-02,  3.13181393e-02, -5.75652197e-02,
            7.04995319e-02, -6.92743529e-03, -9.82947052e-02,
            -4.91717793e-02,  4.06844541e-02, -1.53035461e-03,
            4.68783826e-02,  5.36918640e-03, -1.67432979e-01,
            1.03467651e-01,  3.48554403e-02,  3.20128165e-02,
            4.70223904e-01,  9.19904634e-02,  6.90946281e-02,
            -6.94891065e-02,  3.92344594e-02, -6.30731881e-02,
            2.22810470e-02, -3.87494615e-03,  1.96694940e-01,
            -1.92701817e-02,  2.01028123e-01,  1.89283062e-02,
            -6.97807550e-01,  2.03354478e-01, -1.07645869e-01],
          [ 2.22034067e-01, -1.60748392e-01,  2.42968962e-01,
            -3.35482806e-01, -3.41870189e-02,  1.28819138e-01,
            1.24212839e-01, -3.87125909e-02, -5.60933471e-01,
            7.95257688e-02, -3.60307507e-02,  3.67332071e-01,
            -5.87672107e-02,  7.33083040e-02, -3.94398779e-01,
            -7.60597512e-02,  1.71925854e-02,  1.17799109e-02,
            -2.65986789e-02,  1.98394638e-02, -1.35528380e-02,
            -3.39059532e-02,  9.92002785e-02, -7.92167559e-02,
            9.19176906e-04, -4.89958897e-02,  5.72972372e-02,
            1.21006947e-02,  4.03640568e-02, -1.18844979e-01,
            -2.80744191e-02, -1.74218431e-01, -4.31395955e-02,
            -6.09265082e-02,  3.76862884e-02, -1.11892074e-01],
          [ 2.06562474e-01,  1.73960440e-02, -2.63249487e-01,
            1.38902217e-01, -4.79032584e-02, -2.24852517e-01,
            4.69521992e-02, -3.35566737e-02,  1.37603536e-01,
            -5.11448458e-02,  8.18398222e-02,  1.07205749e-01,
            -1.46739393e-01, -1.30916521e-01, -2.28276670e-01,
            -7.91462511e-02,  6.24803789e-02,  4.59876209e-02,
            8.15130547e-02,  1.46908918e-02, -2.61019613e-03,
            1.13239333e-01,  2.98404664e-01, -1.80148214e-01,
            1.44556239e-01, -3.98542970e-01, -4.15323582e-03,
            4.42554235e-01,  4.46505845e-02, -3.50878686e-02,
            -1.36736231e-02,  1.28197059e-01,  1.92225441e-01,
            9.25138816e-02, -2.71676213e-01, -1.16138257e-01],
          [ 1.91090912e-01, -3.68523598e-02, -6.60930753e-01,
            3.02158773e-01,  1.77503861e-02,  1.00428194e-01,
            -1.10393446e-02,  9.11340117e-03, -7.01573640e-02,
            -3.42316413e-03, -7.93189174e-05,  2.59178817e-01,
            1.22925844e-02,  6.14976510e-02, -1.56667307e-01,
            -5.03374226e-02, -4.95696850e-02, -1.59401018e-02,
            -4.26767953e-02, -5.12050986e-02, -6.04047906e-03,
            5.44762500e-02, -1.07276395e-01, -1.12534806e-01,
            -1.20743208e-01,  3.80993217e-01, -2.20808387e-02,
            -2.89817184e-01,  3.23761255e-02, -6.17432930e-02,
            -3.90686616e-02, -5.96804358e-02, -4.96021062e-02,
            8.57739672e-02, -8.64073634e-02, -1.20384485e-01],
          [ 1.75619304e-01,  1.71932317e-02,  4.29833472e-01,
            8.81271958e-02, -3.94745134e-02, -5.61874844e-02,
            7.05854744e-02,  7.86138419e-03,  4.67237175e-01,
            -1.88353360e-02,  6.92435876e-02, -3.38627174e-02,
            -8.19625556e-02, -4.84902970e-02, -2.62022078e-01,
            -1.48765266e-01,  7.19114691e-02, -1.21600203e-01,
            1.18209779e-01,  2.58331411e-02,  4.69931588e-02,
            9.96347591e-02,  2.32059956e-01, -1.78489253e-01,
            1.77511200e-03,  1.59484446e-01,  3.28991674e-02,
            -4.70239580e-01,  1.65105104e-01, -2.61324756e-02,
            -1.49319443e-04, -8.15570727e-02,  7.44131976e-05,
            8.14437792e-02, -7.25714415e-02, -1.24630690e-01],
          [ 1.60147712e-01, -1.10780589e-01,  2.73144871e-01,
            1.10703602e-01,  2.37337053e-02,  4.52041216e-02,
            1.52682560e-02, -3.83009948e-02,  2.30164632e-01,
            2.54375394e-02, -3.03758867e-02,  8.13979190e-03,
            2.33282149e-02,  3.12441736e-02, -1.84844747e-01,
            2.14728359e-02, -5.53616770e-02, -2.22909674e-02,
            -9.31906551e-02, -1.01961263e-01, -3.32283713e-02,
            8.18983093e-02, -3.90430242e-01,  1.43959653e-02,
            -1.31596243e-02,  4.55893874e-01, -4.22518775e-02,
            5.82709551e-01, -1.36653170e-01, -3.07889320e-02,
            -4.67781313e-02, -6.33331314e-02, -5.06754033e-03,
            3.76623571e-02, -6.18892610e-02, -1.28876895e-01],
          [ 1.44676119e-01, -2.91557442e-02,  2.55934417e-01,
            5.66692650e-01,  3.84408869e-02,  1.04354315e-01,
            -1.37322113e-01,  7.15484237e-03, -1.95520781e-02,
            -2.59401686e-02, -9.82144028e-02,  2.44248882e-01,
            1.52861271e-02,  1.99174404e-01,  2.76121795e-01,
            8.94557908e-02, -1.24152258e-01,  6.37411512e-03,
            -1.13803938e-01, -3.23315486e-02, -3.17632034e-02,
            2.70075332e-02,  2.75091957e-02, -4.90174480e-02,
            -2.08239228e-01, -3.95830333e-01, -5.95310889e-02,
            -4.46558185e-03, -7.16161057e-02, -4.99811508e-02,
            -1.02262713e-01, -2.79212356e-01, -5.11405505e-02,
            2.62467805e-02,  1.03744328e-01, -1.33123115e-01],
          [ 1.29204527e-01, -1.63312718e-01, -1.99243486e-01,
            -2.34051406e-01,  3.55675933e-03,  1.56449080e-02,
            9.30304453e-02, -7.26388171e-02,  1.25461653e-01,
            1.20737530e-01,  4.42517921e-02, -4.18601990e-01,
            -1.94645032e-01,  1.02710314e-01, -7.12260604e-02,
            -6.79927021e-02, -3.08946688e-02, -8.88019353e-02,
            -4.35314551e-02, -2.15784147e-01, -1.86102502e-02,
            5.49090989e-02, -3.75167191e-01, -8.20007622e-02,
            -2.06737250e-01, -3.52603942e-01, -3.86392660e-02,
            -2.84039471e-02,  2.83454835e-01, -2.61564963e-02,
            1.20758023e-02, -2.92337686e-01, -5.17344326e-02,
            3.77417319e-02,  1.23368390e-01, -1.37369320e-01],
          [ 1.13732927e-01, -6.13378249e-02, -1.77854180e-01,
            -4.99198377e-01,  2.01901477e-02,  1.41450047e-01,
            -3.23677920e-02,  9.39797983e-03,  5.04098058e-01,
            1.23931216e-02, -8.47154856e-02,  3.81212860e-01,
            1.21610202e-01,  4.87964153e-02,  2.52459884e-01,
            1.51112108e-02, -4.74717468e-02, -1.84605867e-02,
            -7.36073852e-02,  3.58235948e-02, -7.69592915e-03,
            -7.00120777e-02,  1.28127992e-01,  4.49521616e-02,
            -7.93955289e-04, -3.76549661e-02,  1.04670962e-02,
            7.88062997e-03, -2.23614484e-01, -9.32817012e-02,
            -4.67354655e-02, -1.74636483e-01,  1.47633761e-01,
            -1.42957285e-01,  7.11189136e-02, -1.41615525e-01],
          [ 9.82613564e-02, -1.55768439e-01, -1.11842593e-02,
            6.37831986e-02,  5.79317398e-02,  3.34746271e-01,
            3.84975046e-01, -2.11655404e-02, -4.85437140e-02,
            -4.50517267e-01, -3.28294598e-02, -2.49714255e-01,
            3.28522325e-01, -1.25372112e-01,  2.82705110e-02,
            1.42169207e-01, -8.04641694e-02,  6.62415996e-02,
            -9.59652960e-02, -5.61193414e-02, -4.80792150e-02,
            -4.04721648e-02,  2.45707080e-01,  2.35501617e-01,
            -4.14447524e-02,  4.34486791e-02, -4.62412462e-02,
            4.26126681e-02,  2.55748153e-01, -7.83308148e-02,
            2.59090564e-03, -3.38329338e-02,  1.78729519e-01,
            -3.09782606e-02, -8.34960043e-02, -1.45861730e-01],
          [ 8.27897936e-02, -5.39819747e-02,  5.41151650e-02,
            -2.87518036e-02,  1.98750496e-02, -1.58728033e-01,
            -4.75713938e-01,  1.16178179e-02, -2.98879808e-03,
            2.26475924e-01,  2.46154964e-02,  1.24507852e-01,
            4.07826692e-01, -2.43859500e-01,  1.46053182e-02,
            8.78053382e-02, -7.19747171e-02, -4.02797535e-02,
            -8.92022029e-02, -4.73439731e-02,  2.02829354e-02,
            -9.01956186e-02, -1.16379023e-01,  1.02566876e-01,
            1.27621949e-01, -3.85584086e-02, -1.85301397e-02,
            -1.46384817e-02,  5.42852879e-01, -1.11336805e-01,
            -4.69652563e-02,  1.10105053e-01, -3.25540863e-02,
            -9.18325037e-02, -1.09285243e-01, -1.50107935e-01],
          [ 6.73181787e-02, -1.69579491e-01, -5.90509735e-02,
            -8.87142718e-02, -4.61161807e-02, -1.32888526e-01,
            -4.28256035e-01, -4.96512838e-02, -1.00748278e-01,
            -1.56540096e-01, -1.33985683e-01, -3.31550747e-01,
            3.25447232e-01,  2.73245610e-02, -6.19893037e-02,
            -1.48184791e-01,  1.88705355e-01,  1.62340149e-01,
            1.02853999e-01,  3.19841057e-01, -6.06105961e-02,
            1.69779122e-01,  1.54020518e-01, -8.75391066e-02,
            -2.06520095e-01,  6.03866279e-02,  1.08508043e-01,
            4.56446186e-02, -2.30992153e-01,  6.16142601e-02,
            5.93037927e-04, -2.22505212e-01, -4.13618460e-02,
            1.47342280e-01,  4.37493399e-02, -1.54354155e-01],
          [ 5.18466011e-02,  1.40082181e-01, -2.43853368e-02,
            -9.01594944e-03, -2.02037729e-02, -2.15594158e-01,
            -1.49669036e-01, -2.02583615e-02,  4.76960652e-03,
            -4.28980350e-01, -2.16286242e-01,  2.93388069e-02,
            -2.61512101e-01,  4.32281435e-01,  5.15976362e-02,
            -2.38068718e-02,  1.35174215e-01,  1.65118262e-01,
            1.18229888e-01, -4.75422740e-02, -1.69874616e-02,
            -9.87956077e-02, -6.16191179e-02,  1.92472130e-01,
            4.03664082e-01,  9.86855701e-02,  2.18016505e-02,
            9.58452746e-03,  2.42479756e-01, -9.45590809e-02,
            6.06411323e-02, -1.15035795e-01, -5.60823381e-02,
            -1.10115618e-01,  7.84227401e-02, -1.58600360e-01],
          [ 3.63750085e-02,  2.90070504e-01,  2.58655623e-02,
            -4.51171659e-02, -9.76288766e-02, -7.32196262e-03,
            2.62665208e-02, -1.30719528e-01, -3.34864855e-02,
            1.83281839e-01, -2.03847468e-01, -7.86208585e-02,
            2.39961028e-01,  9.32282284e-02, -1.40201841e-02,
            -1.65743440e-01,  2.50046160e-02,  1.87149823e-01,
            -1.68221984e-02, -6.99453712e-01,  2.46135090e-02,
            9.76792276e-02,  1.59403309e-01,  1.05807781e-01,
            -1.64897703e-02, -3.37719321e-02,  9.97098759e-02,
            -5.71760125e-02, -2.09543109e-01,  1.61970984e-02,
            4.49959114e-02,  1.13044158e-01, -1.33089647e-01,
            6.79383874e-02, -1.17107280e-01, -1.62846550e-01],
          [ 2.09034402e-02,  9.87452939e-02,  3.10002435e-02,
            -3.82550769e-02,  6.49476936e-03, -1.86508909e-01,
            -1.58566430e-01,  1.52609888e-02,  2.44785240e-03,
            -1.72963649e-01,  2.82357018e-02,  6.35804012e-02,
            -4.01134878e-01, -3.48292142e-01, -9.30772051e-02,
            2.69406252e-02, -1.48355186e-01,  6.67649359e-02,
            -1.52495161e-01, -4.16254858e-03, -7.79623985e-02,
            -8.69922712e-02,  1.67651065e-02,  4.43452805e-01,
            -4.69122916e-01,  1.32700158e-02,  1.84264123e-01,
            -4.69396599e-02, -8.76988843e-02, -8.42647329e-02,
            1.80242240e-01,  4.39915545e-02, -3.01284958e-02,
            -4.19178084e-02, -6.55100867e-02, -1.67092770e-01],
          [ 5.43184578e-03, -8.44964292e-03,  5.85759105e-03,
            -7.32589066e-02, -6.53161779e-02,  1.58945680e-01,
            -1.98484868e-01, -2.29594544e-01, -3.62942442e-02,
            -4.60159145e-02,  4.65791941e-01, -1.32931456e-01,
            -1.30874768e-01,  1.82594404e-01,  4.72868867e-02,
            7.68151507e-02, -1.17584936e-01, -7.83182383e-02,
            -5.70569098e-01,  5.07849343e-02, -6.92476258e-02,
            1.45652056e-01,  1.57256410e-01, -2.92076059e-02,
            2.85284370e-01,  2.52744146e-02,  2.82830708e-02,
            -5.04164398e-02, -1.00659683e-01,  5.86346574e-02,
            1.91001222e-02,  8.99196714e-02, -1.54763028e-01,
            1.01448707e-01, -7.42661506e-02, -1.71338975e-01],
          [-1.00397598e-02,  6.89980984e-02,  5.02617331e-03,
            -5.32203764e-02,  1.92967560e-02, -5.64105034e-01,
            3.46719325e-01, -7.40835667e-02, -5.14018210e-03,
            9.32325572e-02,  1.93343818e-01,  3.23573984e-02,
            2.21131876e-01,  3.06417048e-01, -8.70961323e-03,
            4.47171003e-01,  8.35162401e-02,  8.83740187e-02,
            -8.72178078e-02,  1.18704282e-01,  1.05058528e-01,
            -4.56921048e-02,  1.59751941e-02, -3.00876088e-02,
            -2.47394085e-01,  4.93424907e-02, -6.64604902e-02,
            -3.64027135e-02, -1.82686392e-02, -4.59523462e-02,
            -1.26862470e-02,  2.52796169e-02, -4.81151454e-02,
            -2.86283679e-02, -2.56162435e-02, -1.75585181e-01],
          [-2.55113579e-02,  1.63476765e-02, -6.48622513e-02,
            8.53358284e-02, -1.47179626e-02, -2.74279952e-01,
            3.23813617e-01,  1.18787922e-01, -3.12188938e-02,
            1.27388835e-01, -1.47029653e-01, -6.44396339e-03,
            1.59717619e-01, -8.00469816e-02,  4.15628105e-02,
            -3.71895492e-01, -2.58336008e-01, -3.58502686e-01,
            -9.30814072e-02,  2.37474293e-01, -1.02323368e-01,
            7.77886510e-02, -2.62345857e-04,  3.05618107e-01,
            2.69323707e-01, -4.94645983e-02,  7.17321262e-02,
            1.81141701e-02, -7.26979673e-02,  3.66130173e-02,
            3.41478437e-02, -1.42837018e-01, -2.29302347e-01,
            9.40499976e-02,  9.85415503e-02, -1.79831386e-01],
          [-4.09829244e-02,  2.96095997e-01,  5.72670512e-02,
            -1.39296770e-01, -1.60581374e-03,  2.67294142e-02,
            5.13432994e-02,  3.44210893e-01, -4.88008671e-02,
            -1.20673403e-01, -4.54095185e-01, -3.60888802e-02,
            -3.48375738e-02, -3.80728357e-02,  6.19033575e-02,
            2.85812598e-02, -5.49174994e-02,  8.16437509e-03,
            -3.89526159e-01,  1.42197743e-01, -6.57034442e-02,
            9.32944417e-02, -1.29381031e-01, -4.54968363e-01,
            -7.63084590e-02, -1.27602285e-02, -3.93663906e-02,
            -2.22954508e-02,  9.34363678e-02,  4.61584628e-02,
            1.17300354e-01,  1.84356645e-01,  4.64061499e-02,
            2.61230320e-02, -1.38632745e-01, -1.84077591e-01],
          [-5.64545169e-02, -3.65092814e-01, -4.26685773e-02,
            1.75265297e-02, -1.79290678e-03,  7.54252076e-02,
            -2.16403184e-03,  1.22491851e-01,  4.61655157e-03,
            9.93698239e-02, -2.86250204e-01,  1.17600495e-02,
            -1.76643163e-01, -1.61555171e-01,  4.21675071e-02,
            4.96386349e-01,  2.84064054e-01, -1.88499331e-01,
            5.03461063e-02, -9.29289460e-02,  2.72047639e-01,
            1.54824242e-01,  7.62812719e-02,  9.09931362e-02,
            1.82046860e-01, -1.51961623e-02,  1.57171339e-01,
            -2.52939817e-02, -6.88583925e-02,  8.74516144e-02,
            1.06507227e-01,  3.63174151e-03, -2.16592148e-01,
            1.95526704e-01, -2.63463091e-02, -1.88323811e-01],
          [-7.19260871e-02,  1.53307199e-01,  2.98810583e-02,
            -1.76042188e-02,  4.68952209e-02,  2.30930567e-01,
            -1.91631261e-02, -3.50371659e-01, -1.39247498e-03,
            -3.16982158e-02,  3.19441818e-02,  1.38011038e-01,
            1.15297228e-01,  1.21593997e-01,  1.12343794e-02,
            -6.25559241e-02,  2.27593221e-02, -1.95765942e-01,
            2.61839062e-01,  1.88924655e-01,  1.47905156e-01,
            3.61047573e-02, -1.53986499e-01,  4.26004231e-02,
            -1.01659156e-01, -9.87078920e-02, -1.97795078e-01,
            2.87956242e-02,  2.66166143e-02,  2.03926936e-02,
            6.36121154e-01,  1.17329828e-01, -1.68884546e-02,
            1.05052806e-01, -1.36004210e-01, -1.92570001e-01],
          [-8.73977244e-02,  2.91939259e-01, -6.38535023e-02,
            1.23778999e-01,  2.33115517e-02,  8.99281502e-02,
            -2.38235518e-02,  2.54457176e-01, -2.92873345e-02,
            1.45903289e-01,  2.51857221e-01, -1.22888424e-01,
            4.71667722e-02, -1.51163086e-01, -6.75680041e-02,
            1.34960130e-01, -5.27166612e-02,  5.85827529e-02,
            6.49949759e-02, -6.27990216e-02,  7.91215152e-02,
            -2.11644500e-01,  1.25666901e-01, -2.19153777e-01,
            1.45102561e-01,  9.46507752e-02,  2.63710856e-01,
            1.36273995e-01, -2.85680946e-02, -9.64817554e-02,
            3.51572961e-01, -3.73799771e-01,  7.54300505e-02,
            -1.52278930e-01,  2.77134597e-01, -1.96816236e-01],
          [-1.02869295e-01,  4.54483837e-01, -3.16920318e-02,
            -9.15080402e-03,  4.94015254e-02,  2.09832817e-01,
            9.22076330e-02, -3.92193407e-01, -1.33265834e-03,
            1.03313603e-01, -7.82989189e-02,  8.86598602e-03,
            -9.18587223e-02, -1.70766622e-01,  5.54255210e-02,
            2.28601284e-02,  1.81634039e-01,  4.14796174e-02,
            3.81892845e-02,  2.48120666e-01,  1.65915981e-01,
            2.87097245e-02, -2.50649545e-02,  4.36540544e-02,
            -5.01171201e-02,  3.54694985e-02,  1.90053612e-01,
            9.52630565e-02,  1.70738876e-01,  3.70882489e-02,
            -4.90600616e-01, -9.28841755e-02, -8.13470930e-02,
            8.31348598e-02,  5.93565181e-02, -2.01062426e-01],
          [-1.18340865e-01, -6.85950592e-02,  4.95309308e-02,
            -1.77844893e-02, -9.69045609e-02,  2.31995173e-02,
            -1.06131600e-03,  2.21603140e-01, -6.05566725e-02,
            -2.82245725e-01,  2.64784724e-01,  8.62200931e-02,
            1.37575060e-01,  1.50092602e-01,  4.38311473e-02,
            -1.27834529e-01, -1.75913945e-02, -2.03415841e-01,
            1.48476526e-01, -7.80855790e-02,  2.29345813e-01,
            3.37421596e-02, -3.02611887e-01, -3.64654101e-02,
            -4.98286486e-02, -1.24875009e-01,  5.32554924e-01,
            -5.55246398e-02, -8.19649324e-02,  4.32646945e-02,
            -1.92818239e-01,  1.91410363e-01,  1.91146538e-01,
            -1.30635314e-02, -1.27977282e-01, -2.05308631e-01],
          [-1.33812457e-01,  5.83807267e-02,  6.38746191e-03,
            -6.32736981e-02,  2.60766506e-01,  1.92557305e-01,
            -4.26477045e-02,  5.47973156e-01,  1.53431622e-02,
            2.03396276e-01,  2.18420655e-01,  1.71779748e-02,
            -7.09848702e-02,  2.39939511e-01, -2.50959713e-02,
            -1.48106590e-01,  1.51656091e-01,  1.71890616e-01,
            7.37760216e-02,  5.53064533e-02,  1.98505912e-02,
            9.67100039e-02,  1.37430176e-01,  2.82746285e-01,
            -1.24559112e-01,  1.80215873e-02, -2.68079907e-01,
            9.55012143e-02,  1.30839288e-01,  8.27972442e-02,
            -9.96278524e-02,  4.17835526e-02, -4.81917933e-02,
            1.98767141e-01, -6.95911944e-02, -2.09554836e-01],
          [-1.49284035e-01, -7.56456144e-03, -8.76261014e-03,
            2.92932428e-02, -8.39372516e-01,  5.67366369e-02,
            -2.41059046e-02,  8.43372419e-02, -2.29054149e-02,
            3.72556150e-02,  3.59098194e-03, -3.51436548e-02,
            -4.86128107e-02, -4.90781479e-02, -2.96334457e-02,
            2.16081198e-02, -6.04292788e-02,  1.73466746e-02,
            5.54120354e-02,  4.32790630e-02,  1.27067477e-01,
            -9.41377804e-02, -1.37587115e-02,  7.06801787e-02,
            -1.22610051e-02,  2.18931045e-02, -3.70597780e-01,
            -1.30672632e-02, -4.53533195e-02, -1.70034133e-02,
            -1.13316208e-01, -3.45941707e-02,  1.05737671e-01,
            -2.95185428e-02,  2.46357918e-02, -2.13801056e-01],
          [-1.64755657e-01, -1.91551998e-01,  1.24477036e-02,
            1.76897332e-01, -1.70191415e-02,  2.34046783e-02,
            6.76611960e-02, -1.21719569e-01, -1.60261299e-02,
            2.84169883e-01, -7.72131458e-02, -4.39732298e-02,
            -6.60723150e-02,  8.68341923e-02,  7.35200867e-02,
            -1.56345084e-01,  4.99212921e-01, -9.53519195e-02,
            -1.69593558e-01,  3.12364921e-02, -4.14223462e-01,
            -2.19161183e-01, -7.49167113e-04,  4.25142385e-02,
            -2.26298310e-02,  3.90600637e-02,  1.34113848e-01,
            -4.32782359e-02, -2.25105719e-03, -8.36708769e-02,
            7.53742829e-02,  1.09890841e-01,  3.47145647e-01,
            -1.67040601e-01, -4.17540558e-02, -2.18047246e-01],
          [-1.80227250e-01, -3.65751952e-01,  1.95310116e-02,
            3.56181487e-02, -2.47674435e-02, -2.56252866e-02,
            1.70394495e-01, -1.01341322e-01,  6.43750429e-02,
            -1.18520278e-02,  7.76712969e-02,  1.21111691e-01,
            -7.56260678e-02, -1.32285401e-01,  2.50612080e-01,
            -2.70852149e-01, -9.66061503e-02,  4.63890702e-01,
            5.18286489e-02,  1.14975851e-02,  7.05922395e-02,
            7.95801077e-03,  3.40116471e-02, -2.50298321e-01,
            -4.72176410e-02,  7.11330771e-02,  7.71585703e-02,
            7.12307394e-02,  1.51480496e-01,  4.94032800e-02,
            9.26278085e-02,  1.93590626e-01, -3.63108933e-01,
            -1.36400744e-01,  1.46016315e-01, -2.22293481e-01],
          [-1.95698813e-01,  8.16941485e-02,  6.35532150e-03,
            -5.50320372e-02,  1.45350844e-01, -7.66825154e-02,
            -1.48402769e-02,  8.44644289e-03, -3.05129532e-02,
            -3.45072865e-01,  1.88118920e-01,  1.39703169e-01,
            9.01852995e-02, -3.05740625e-01, -7.54492134e-02,
            6.51175901e-02,  2.45817453e-01, -1.89270392e-01,
            1.16880536e-01, -2.26171866e-01, -3.72853994e-01,
            5.43844700e-03, -1.24716990e-01, -1.48458153e-01,
            5.83554097e-02, -8.44632387e-02, -3.41172040e-01,
            -5.05601391e-02, -1.60052970e-01,  5.74440435e-02,
            -1.45993277e-01, -4.03214097e-02, -2.16732427e-01,
            -2.84256153e-02,  1.41579702e-01, -2.26539686e-01],
          [-2.11170420e-01, -6.31088763e-02,  8.17671046e-03,
            -5.57366088e-02,  6.94130734e-02,  3.52174342e-02,
            -6.57851174e-02, -9.82191563e-02, -1.27271414e-02,
            1.43996403e-01, -1.19659491e-01, -5.62400967e-02,
            -1.02117673e-01,  1.46197915e-01, -6.46053180e-02,
            2.75428176e-01, -5.38663089e-01,  1.51460487e-02,
            3.81278455e-01,  1.08411210e-02, -4.44346756e-01,
            4.02242467e-02,  9.23668295e-02, -7.21167400e-02,
            3.91138941e-02,  4.99221608e-02,  9.94546860e-02,
            -3.87978405e-02,  1.93843860e-02,  8.32882449e-02,
            -1.15623131e-01,  8.08125958e-02,  1.40358344e-01,
            1.01261795e-01, -5.90205789e-02, -2.30785877e-01],
          [-2.26641983e-01, -1.44536331e-01,  8.91233422e-03,
            5.05167954e-02,  3.87359351e-01, -1.25706807e-01,
            -9.50697213e-02, -1.42298609e-01, -7.01352954e-02,
            -3.15868692e-03, -1.33074358e-01, -1.18453935e-01,
            -7.71054849e-02, -4.75535467e-02, -1.50268868e-01,
            -1.44392461e-01, -1.82032049e-01, -1.19762598e-02,
            -1.21959276e-01, -6.38470054e-02,  4.80738163e-01,
            -1.59658909e-01,  2.71296166e-02, -4.31644246e-02,
            1.02411315e-01,  2.07743910e-03, -2.89108336e-01,
            -1.03720047e-01, -2.01758668e-01, -2.16420572e-02,
            -1.27163813e-01, -7.36601278e-03,  3.14732850e-01,
            -1.12868495e-01,  3.11465543e-02, -2.35032097e-01]], dtype=float32), array([-1.89882166e+03, -1.79985218e-04, -1.70435800e-04, -1.27975552e-04,
          -1.24901737e-04, -1.24676313e-04, -1.16428266e-04, -1.06598200e-04,
          -1.00050034e-04, -9.61478145e-05, -8.36294785e-05, -6.41566730e-05,
          -4.51904889e-05, -2.39018827e-05, -1.49146554e-05, -9.43070791e-06,
          -8.04440424e-06,  1.51055592e-05,  2.01099483e-05,  2.64523860e-05,
            3.25085311e-05,  5.15936626e-05,  5.31896258e-05,  7.24942220e-05,
            9.04739063e-05,  1.04830775e-04,  1.08393360e-04,  1.37811687e-04,
            1.49946762e-04,  1.86386926e-04,  1.89535742e-04,  2.40968098e-03,
            2.56012683e-03,  2.69382820e-03,  3.27441283e-03,  2.52088105e+04],
          dtype=float32)),
        mlir_module_text=r"""
module @jit__lambda_ {
  func.func public @main() -> (tensor<36x36xf32> {jax.result_info = "[0]"}, tensor<36xf32> {jax.result_info = "[1]"}) {
    %0 = stablehlo.iota dim = 0 : tensor<1296xf32>
    %1 = stablehlo.reshape %0 : (tensor<1296xf32>) -> tensor<36x36xf32>
    %2 = stablehlo.transpose %1, dims = [1, 0] : (tensor<36x36xf32>) -> tensor<36x36xf32>
    %3 = stablehlo.add %1, %2 : tensor<36x36xf32>
    %4 = stablehlo.constant dense<2.000000e+00> : tensor<f32>
    %5 = stablehlo.broadcast_in_dim %4, dims = [] : (tensor<f32>) -> tensor<36x36xf32>
    %6 = stablehlo.divide %3, %5 : tensor<36x36xf32>
    %7 = call @tril(%6) : (tensor<36x36xf32>) -> tensor<36x36xf32>
    %8 = stablehlo.custom_call @cusolver_syevd(%7) {api_version = 2 : i32, backend_config = "\00\00\00\00\00\00\00\00\01\00\00\00$\00\00\00Y\98\00\00", operand_layouts = [dense<[0, 1]> : tensor<2xindex>], output_operand_aliases = [#stablehlo.output_operand_alias<output_tuple_indices = [0], operand_index = 0, operand_tuple_indices = []>], result_layouts = [dense<[0, 1]> : tensor<2xindex>, dense<0> : tensor<1xindex>, dense<> : tensor<0xindex>, dense<0> : tensor<1xindex>]} : (tensor<36x36xf32>) -> tuple<tensor<36x36xf32>, tensor<36xf32>, tensor<i32>, tensor<39001xf32>>
    %9 = stablehlo.get_tuple_element %8[0] : (tuple<tensor<36x36xf32>, tensor<36xf32>, tensor<i32>, tensor<39001xf32>>) -> tensor<36x36xf32>
    %10 = stablehlo.get_tuple_element %8[1] : (tuple<tensor<36x36xf32>, tensor<36xf32>, tensor<i32>, tensor<39001xf32>>) -> tensor<36xf32>
    %11 = stablehlo.get_tuple_element %8[2] : (tuple<tensor<36x36xf32>, tensor<36xf32>, tensor<i32>, tensor<39001xf32>>) -> tensor<i32>
    %12 = stablehlo.get_tuple_element %8[3] : (tuple<tensor<36x36xf32>, tensor<36xf32>, tensor<i32>, tensor<39001xf32>>) -> tensor<39001xf32>
    %13 = stablehlo.constant dense<0> : tensor<i32>
    %14 = stablehlo.broadcast_in_dim %13, dims = [] : (tensor<i32>) -> tensor<i32>
    %15 = stablehlo.compare  EQ, %11, %14,  SIGNED : (tensor<i32>, tensor<i32>) -> tensor<i1>
    %16 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1x1xi1>
    %17 = stablehlo.constant dense<0x7FC00000> : tensor<f32>
    %18 = stablehlo.broadcast_in_dim %17, dims = [] : (tensor<f32>) -> tensor<36x36xf32>
    %19 = stablehlo.broadcast_in_dim %16, dims = [0, 1] : (tensor<1x1xi1>) -> tensor<36x36xi1>
    %20 = stablehlo.select %19, %9, %18 : tensor<36x36xi1>, tensor<36x36xf32>
    %21 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1xi1>
    %22 = stablehlo.constant dense<0x7FC00000> : tensor<f32>
    %23 = stablehlo.broadcast_in_dim %22, dims = [] : (tensor<f32>) -> tensor<36xf32>
    %24 = stablehlo.broadcast_in_dim %21, dims = [0] : (tensor<1xi1>) -> tensor<36xi1>
    %25 = stablehlo.select %24, %10, %23 : tensor<36xi1>, tensor<36xf32>
    return %20, %25 : tensor<36x36xf32>, tensor<36xf32>
  }
  func.func private @tril(%arg0: tensor<36x36xf32>) -> tensor<36x36xf32> {
    %0 = stablehlo.iota dim = 0 : tensor<36x36xi32>
    %1 = stablehlo.constant dense<0> : tensor<i32>
    %2 = stablehlo.broadcast_in_dim %1, dims = [] : (tensor<i32>) -> tensor<36x36xi32>
    %3 = stablehlo.add %0, %2 : tensor<36x36xi32>
    %4 = stablehlo.iota dim = 1 : tensor<36x36xi32>
    %5 = stablehlo.compare  GE, %3, %4,  SIGNED : (tensor<36x36xi32>, tensor<36x36xi32>) -> tensor<36x36xi1>
    %6 = stablehlo.constant dense<0.000000e+00> : tensor<f32>
    %7 = stablehlo.broadcast_in_dim %6, dims = [] : (tensor<f32>) -> tensor<36x36xf32>
    %8 = stablehlo.select %5, %arg0, %7 : tensor<36x36xi1>, tensor<36x36xf32>
    return %8 : tensor<36x36xf32>
  }
}
""",
        mlir_module_serialized=b"ML\xefR\x03MLIRxxx-trunk\x00\x01-\x05\x01\x05\x01\x03\x05\x03\x1d\x07\t\x0b\r\x0f\x11\x13\x15\x17\x19\x1b\x1d\x1f!\x03^\x02\xeb5\x01\x95\x0f\x17\x13\x07\x0f\x0b\x0b\x0b\x0b\x0b\x17\x0b\x0b\x0b\x0b\x13\x0b\x13\x0f\x0b\x0b\x17\x0f\x13\x13\x0b33\x0b\x0f\x0b\x0b\x13\x0f\x0b\x1b\x0f\x0b\x13\x0f\x0b\x0f\x0b\x0f\x0b\x0f\x0b\x13\x0b\x0f\x0b\x0f\x0b\x13\x0b\x13\x0bK\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x13\x13\x13\x13\x1b\x13\x13\x03W\x0b\x0b\x0f\x0b\x0bO/\x0b\x13\x13\x0b\x13\x0b\x0b\x0b\x0b\x0b\x0b\x0f\x1f\x0f\x0f\x0b\x1fO\x1f\x0b\x0b\x0b\x0b\x0f\x0f\x17\x1b\x0f\x0f\x0f\x0f\x0f\x0b\x1fO/\x035\x17\x0f\x07\x0f\x07\x13\x07\x07\x17\x07\x17\x13\x1b\x17\x17\x13\x17\x1b\x13\x13\x13\x0f\x17\x13\x13\x13\x02v\x08\x1d\x85\x03\x17\x11R\x04\x01\x03\x03\x13\xbd\x1f\x1d9\x03\x05#\x05%\x05'\x05)\x05+\x17\x11N\x04\x01\x05-\x05/\x051\x053\x03\x03!\xb9\x055\x03\x03\x0b\xbb\x1d?\x03\x057\x059\x17\x11F\x04\x01\x1dm\x15\x03\x03\x0b\xe5\x03\x03\x0f3\x05;\x03\x0b\x17\x95\x19\xa3\x1b\xa5\x0f\xaf\x1d\xb1\x03\x0b\x17\x99\x19\xb5\x1b\x99\x0f\x9b\x1d\xb7\x05=\x1d=\x03\x05?\x05A\x03\x03!\xbf\x1dE\x03\x05C\x03\x05'\x9d)\xc1\x1dK\x03\x05E\x03\x03\x0b\xc3\x1dQ\x03\x05G\x1dU\x03\x05I\x1dY+\x05K\x1d]+\x05M\x03\x03a\xc5\x05O\x1de\x15\x05Q\x1di\x15\x05S\x03\x03\x0b\xc7\x05U\x03\x03q\x9b\x05W\x03\x11u\xc9w\xcby\xcd{\x95}\xcf\x7f\xd1\x81\xd3\x83\xd7\x05Y\x05[\x05]\x05_\x05a\x05c\x05e\x05g\x05i\x03\x03\r\xdb\x03\x03\r\xdd\x03\x03\r\xdf\x03\x03\r\xe1\x03\x05'\x9d)\xe3\x03\x03\x13\xe7\x03\x03\x13\xe9\x03\x01\x1dk\x03\x03\xb3\x1dm\t\x07\x1f%!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f'\x11\x00\x00\x00\x00\x00\x00\x00\x00#\x1b\x03\x05\xa7\xab\r\x03\x97\xa9\x1do\r\x03\x97\xad\x1dq\x1ds\x1du\r\x01#\x1d\x1dw\x13\r\x01\x1f\x07\t\x00\x00\x00\x00\x1f\x1f\x01\x13\r\x05\x07\x05\x1f\x03\t\x00\x00\x00\x00\x1f\x17!\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x03\t\x00\x00\x00@\x0b\x05\x1dy\x1d{\x05\x01\x03\x03\x9f\x03\x03\xd5\x15\x03\x01\x01\x01\x03\t\x9f\xa1\xd9\xa1\x1f)\x01\x13\x05\x01\x13\x05\x05\x13\x05\t\x13\x05\r\x07\x01\x1f\x03\t\x00\x00\xc0\x7f\x1f\x17!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f3\x11\x00\x00\x00\x00\x00\x00\x00\x00)\x05\x91\x91\t)\x01\t\x1b)\x01\x05\t)\x03\x91\t\x1d\x01)\x05\x91\x91\x05\x13)\x05\x91\x91\x0f)\x03\t\r)\x03\x94\x85\t\t\x11\x01\x05\x01\x0b\x11\x03\x01\x03\x01)\x03\x01\r)\x03\x82(\t/\t\x01\x0b\x07\x19)\x03\t\x13)\x03\x05\x13)\x03\x01\x13)\x01\x0f)\x05\x05\x05\x0f)\x03\x05\x0f)\x03\x91\x0f)\x03\x05\r\x04\xc6\x04\x05\x01\x11\x071\x07\x03\x01\t\r\x11\x075\x05\x035m\t\x03W\x1f\x03!\x15\x06[\x03\x01\x03\x01\x17\x07c_\x03\x01\x03\x03\x0f\x06g\x03\x01\x05\x03\x05\x05\x03\x07k\x03\x03\x03\x07-\x05\x03\x01\x03\t\x19\x06-\x03\x01\x05\x07\x0b\x1b\x07\to\x03\x01\x03\r\x1d\x07\x01s\x03#\x03\x0f\x07\x07\x01\x87\x03\x01\x03\x11\x07\x07\x01\x89\x03\x0b\x03\x11\x07\x07\x01\x8b\x03\x07\x03\x11\x07\x07\x01\x8d\x03\x19\x03\x11\x05\x03\x01#\x03\x07\x03\x07\x01\x05\x03\x07\x03\x1b\x11\x07\x01\x8f\x03+\x05\x17\x1d\x03\x07\x01\x05\x03-\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x01\x03#\x03\x07\x01\x91\x03\x15\x03!\x0b\x06\x01\x03\x01\x07'\x13%\x03\x07\x01\x05\x03/\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x0b\x03-\x03\x07\x01\x93\x031\x03+\x0b\x06\x01\x03\x0b\x071\x15/\x13\x04\x07\x05)3\r\x11\t7\x05\x03\x15+\x03\x01\x07\t\x03;\x1f\x03\x11\x05\x03\t#\x03\x07\x03\x07%\x05\x03\x11\x03\x05\x0f\x06%\x03\x11\x05\x03\x07\t\x03CA\x03\x11\x11\x07IG\x03\x15\x05\t\x0b\x05\x03\tM\x03\x03\x03\x07O\x05\x03\x01\x03\x0f\x0b\x06S\x03\x01\x07\r\x01\x11\x13\x04\t\x03\x13\x06\x03\x01\x05\x01\x00.\x1a}\x1f+\x11\x0f\x0b\t\t\x0b!\x7f\x1f/!!)#\x1f\x19\x0f99m\x19\x89\x8dW\xb7K\x9fM\x9f\x96\x04\x1b+\x1b\x1f\x1f\x15\x1d\x15+\x83\x13\r\r\x1f\x11\x15\x1b\x17\x15\x17\x0f\x11\x15\x11+\x19)\x0f\x0b\x11builtin\x00vhlo\x00module\x00broadcast_in_dim_v1\x00constant_v1\x00get_tuple_element_v1\x00iota_v1\x00select_v1\x00func_v1\x00add_v1\x00compare_v1\x00return_v1\x00reshape_v1\x00transpose_v1\x00divide_v1\x00call_v1\x00custom_call_v1\x00value\x00index\x00sym_name\x00third_party/py/jax/experimental/jax2tf/tests/back_compat_test.py\x00broadcast_dimensions\x00arg_attrs\x00function_type\x00res_attrs\x00sym_visibility\x00iota_dimension\x00compare_type\x00comparison_direction\x00jit__lambda_\x00jit(<lambda>)/jit(main)/pjit[in_shardings=(UnspecifiedValue,) out_shardings=(UnspecifiedValue,) resource_env=None donated_invars=(False,) name=tril in_positional_semantics=(<_PositionalSemantics.GLOBAL: 1>,) out_positional_semantics=_PositionalSemantics.GLOBAL keep_unused=False inline=False]\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(36, 36) dimension=0]\x00jit(<lambda>)/jit(main)/jit(tril)/add\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(36, 36) dimension=1]\x00jit(<lambda>)/jit(main)/jit(tril)/ge\x00jit(<lambda>)/jit(main)/jit(tril)/broadcast_in_dim[shape=(36, 36) broadcast_dimensions=()]\x00jit(<lambda>)/jit(main)/jit(tril)/select_n\x00jit(<lambda>)/jit(main)/iota[dtype=float32 shape=(1296,) dimension=0]\x00jit(<lambda>)/jit(main)/reshape[new_sizes=(36, 36) dimensions=None]\x00permutation\x00jit(<lambda>)/jit(main)/transpose[permutation=(1, 0)]\x00jit(<lambda>)/jit(main)/add\x00jit(<lambda>)/jit(main)/div\x00callee\x00api_version\x00backend_config\x00call_target_name\x00called_computations\x00has_side_effect\x00operand_layouts\x00output_operand_aliases\x00result_layouts\x00jit(<lambda>)/jit(main)/eigh[lower=True sort_eigenvalues=True]\x00jax.result_info\x00tril\x00[0]\x00[1]\x00main\x00public\x00private\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00$\x00\x00\x00Y\x98\x00\x00\x00cusolver_syevd\x00",
        xla_call_module_version=4,
    ),  # End paste

    # Pasted from the test output (see back_compat_test.py module docstring)
    f64_syevj=dict(
        testdata_version=1,
        platform='cuda',
        custom_call_targets=['cusolver_syevj'],
        serialized_date=datetime.date(2023, 3, 17),
        inputs=(),
        expected_outputs=(array([[ 6.1857700048412179e-01, -7.9870412160195655e-05,
            -7.1795133407817180e-02,  7.2651725579187088e-01,
            -5.8816812454044016e-04, -1.0752133550364418e-01,
            -1.9695247974936425e-01,  1.8446994643771727e-01],
          [ 4.7070881487314487e-01,  3.3071017759156432e-05,
            -5.9630159401629157e-01, -4.7856902268752244e-01,
            -1.4151478943184035e-03, -2.5017522435505674e-01,
            2.8106392345809550e-01,  2.2856669794666581e-01],
          [ 3.2284062926217122e-01, -5.1104181032785456e-01,
            2.4098685972870454e-01, -3.2057977627137213e-01,
            6.0128498619340851e-04,  5.5435726441071020e-01,
            -3.0349043125069775e-01,  2.7266344945561433e-01],
          [ 1.7497244365119549e-01,  4.1809211960021736e-01,
            5.7112844532216078e-01, -3.1146378582869927e-01,
            -4.8989605706119613e-04, -4.4689091764000977e-01,
            -2.6709076241922963e-01,  3.1676020096456298e-01],
          [ 2.7104258040218803e-02,  4.2941995817157164e-01,
            1.1304358388496584e-01,  9.3073375918824142e-02,
            -4.7236149166811120e-01,  4.6617552271070906e-01,
            4.7197416944525139e-01,  3.6085695247351168e-01],
          [-1.2076392757075657e-01, -3.8434927079561992e-01,
            2.9171425263113138e-01,  1.5624558970245273e-01,
            4.3260383504376299e-01, -2.0278835428567779e-01,
            5.7959048064074936e-01,  4.0495370398246017e-01],
          [-2.6863211318173014e-01,  3.6363990709349564e-01,
            -3.3163183889685732e-01,  4.2836063092320187e-02,
            5.6343802845177837e-01,  2.7652818360156795e-01,
            -2.9700444618985122e-01,  4.4905045549140854e-01],
          [-4.1650029879270561e-01, -3.1571410434740910e-01,
            -2.1714457524599659e-01,  9.1940300282126255e-02,
            -5.2178844473770358e-01, -2.8968513893859849e-01,
            -2.6809045393495168e-01,  4.9314720700035708e-01]]), array([-2.4598804776133605e+01, -2.8026300235964570e-15,
          -1.8958980326674837e-15,  1.5553235693581772e-15,
            1.6670762548207520e-15,  2.2405283578797194e-15,
            5.4086800892994285e-15,  2.7659880477613365e+02])),
        mlir_module_text="""
module @jit__lambda_ {
  func.func public @main() -> (tensor<8x8xf64> {jax.result_info = "[0]"}, tensor<8xf64> {jax.result_info = "[1]"}) {
    %0 = stablehlo.iota dim = 0 : tensor<64xf64>
    %1 = stablehlo.reshape %0 : (tensor<64xf64>) -> tensor<8x8xf64>
    %2 = stablehlo.transpose %1, dims = [1, 0] : (tensor<8x8xf64>) -> tensor<8x8xf64>
    %3 = stablehlo.add %1, %2 : tensor<8x8xf64>
    %4 = stablehlo.constant dense<2.000000e+00> : tensor<f64>
    %5 = stablehlo.broadcast_in_dim %4, dims = [] : (tensor<f64>) -> tensor<8x8xf64>
    %6 = stablehlo.divide %3, %5 : tensor<8x8xf64>
    %7 = call @tril(%6) : (tensor<8x8xf64>) -> tensor<8x8xf64>
    %8 = stablehlo.custom_call @cusolver_syevj(%7) {api_version = 2 : i32, backend_config = "\01\00\00\00\00\00\00\00\01\00\00\00\08\00\00\00M\08\00\00", operand_layouts = [dense<[0, 1]> : tensor<2xindex>], output_operand_aliases = [#stablehlo.output_operand_alias<output_tuple_indices = [0], operand_index = 0, operand_tuple_indices = []>], result_layouts = [dense<[0, 1]> : tensor<2xindex>, dense<0> : tensor<1xindex>, dense<> : tensor<0xindex>, dense<0> : tensor<1xindex>]} : (tensor<8x8xf64>) -> tuple<tensor<8x8xf64>, tensor<8xf64>, tensor<i32>, tensor<2125xf64>>
    %9 = stablehlo.get_tuple_element %8[0] : (tuple<tensor<8x8xf64>, tensor<8xf64>, tensor<i32>, tensor<2125xf64>>) -> tensor<8x8xf64>
    %10 = stablehlo.get_tuple_element %8[1] : (tuple<tensor<8x8xf64>, tensor<8xf64>, tensor<i32>, tensor<2125xf64>>) -> tensor<8xf64>
    %11 = stablehlo.get_tuple_element %8[2] : (tuple<tensor<8x8xf64>, tensor<8xf64>, tensor<i32>, tensor<2125xf64>>) -> tensor<i32>
    %12 = stablehlo.get_tuple_element %8[3] : (tuple<tensor<8x8xf64>, tensor<8xf64>, tensor<i32>, tensor<2125xf64>>) -> tensor<2125xf64>
    %13 = stablehlo.constant dense<0> : tensor<i32>
    %14 = stablehlo.broadcast_in_dim %13, dims = [] : (tensor<i32>) -> tensor<i32>
    %15 = stablehlo.compare  EQ, %11, %14,  SIGNED : (tensor<i32>, tensor<i32>) -> tensor<i1>
    %16 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1x1xi1>
    %17 = stablehlo.constant dense<0x7FF8000000000000> : tensor<f64>
    %18 = stablehlo.broadcast_in_dim %17, dims = [] : (tensor<f64>) -> tensor<8x8xf64>
    %19 = stablehlo.broadcast_in_dim %16, dims = [0, 1] : (tensor<1x1xi1>) -> tensor<8x8xi1>
    %20 = stablehlo.select %19, %9, %18 : tensor<8x8xi1>, tensor<8x8xf64>
    %21 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1xi1>
    %22 = stablehlo.constant dense<0x7FF8000000000000> : tensor<f64>
    %23 = stablehlo.broadcast_in_dim %22, dims = [] : (tensor<f64>) -> tensor<8xf64>
    %24 = stablehlo.broadcast_in_dim %21, dims = [0] : (tensor<1xi1>) -> tensor<8xi1>
    %25 = stablehlo.select %24, %10, %23 : tensor<8xi1>, tensor<8xf64>
    return %20, %25 : tensor<8x8xf64>, tensor<8xf64>
  }
  func.func private @tril(%arg0: tensor<8x8xf64>) -> tensor<8x8xf64> {
    %0 = stablehlo.iota dim = 0 : tensor<8x8xi32>
    %1 = stablehlo.constant dense<0> : tensor<i32>
    %2 = stablehlo.broadcast_in_dim %1, dims = [] : (tensor<i32>) -> tensor<8x8xi32>
    %3 = stablehlo.add %0, %2 : tensor<8x8xi32>
    %4 = stablehlo.iota dim = 1 : tensor<8x8xi32>
    %5 = stablehlo.compare  GE, %3, %4,  SIGNED : (tensor<8x8xi32>, tensor<8x8xi32>) -> tensor<8x8xi1>
    %6 = stablehlo.constant dense<0.000000e+00> : tensor<f64>
    %7 = stablehlo.broadcast_in_dim %6, dims = [] : (tensor<f64>) -> tensor<8x8xf64>
    %8 = stablehlo.select %5, %arg0, %7 : tensor<8x8xi1>, tensor<8x8xf64>
    return %8 : tensor<8x8xf64>
  }
}
""",
        mlir_module_serialized=b"ML\xefR\x03MLIRxxx-trunk\x00\x01-\x05\x01\x05\x01\x03\x05\x03\x1d\x07\t\x0b\r\x0f\x11\x13\x15\x17\x19\x1b\x1d\x1f!\x03^\x02\xeb5\x01\x95\x0f\x17\x13\x07\x0f\x0b\x0b\x0b\x0b\x0b\x17\x0b\x0b\x0b\x0b\x13\x0b\x13\x0f\x0b\x0b\x17\x0f\x13\x13\x0b33\x0b\x0f\x0b\x0b\x13\x0f\x0b\x1b\x0f\x0b\x13\x0f\x0b\x0f\x0b\x0f\x0b\x0f\x0b\x13\x0b\x0f\x0b\x0f\x0b\x13\x0b\x13\x0bK\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x13\x13\x13\x13\x1b\x13\x13\x03W\x0b\x0b\x0f\x0b\x0bO/\x0b\x13\x13\x0b\x13\x0b\x0b\x0b\x0b\x0b\x0b\x0f\x1f\x0f\x0f\x0b/O/\x0b\x0b\x0b\x0b\x0f\x0f\x17\x1b\x0f\x0f\x0f\x0f\x0f\x0b/O/\x035\x17\x0f\x07\x0f\x07\x13\x07\x07\x17\x07\x17\x13\x17\x17\x17\x13\x17\x1b\x13\x13\x13\x0f\x17\x13\x13\x13\x02\xa2\x08\x1d\x85\x03\x17\x116\x04\x01\x03\x03\x13\xbd\x1f\x1d9\x03\x05#\x05%\x05'\x05)\x05+\x17\x112\x04\x01\x05-\x05/\x051\x053\x03\x03!\xb9\x055\x03\x03\x0b\xbb\x1d?\x03\x057\x059\x17\x11*\x04\x01\x1dm\x15\x03\x03\x0b\xe5\x03\x03\x0f3\x05;\x03\x0b\x17\x95\x19\xa3\x1b\xa5\x0f\xaf\x1d\xb1\x03\x0b\x17\x99\x19\xb5\x1b\x99\x0f\x9b\x1d\xb7\x05=\x1d=\x03\x05?\x05A\x03\x03!\xbf\x1dE\x03\x05C\x03\x05'\x9d)\xc1\x1dK\x03\x05E\x03\x03\x0b\xc3\x1dQ\x03\x05G\x1dU\x03\x05I\x1dY+\x05K\x1d]+\x05M\x03\x03a\xc5\x05O\x1de\x15\x05Q\x1di\x15\x05S\x03\x03\x0b\xc7\x05U\x03\x03q\x9b\x05W\x03\x11u\xc9w\xcby\xcd{\x95}\xcf\x7f\xd1\x81\xd3\x83\xd7\x05Y\x05[\x05]\x05_\x05a\x05c\x05e\x05g\x05i\x03\x03\r\xdb\x03\x03\r\xdd\x03\x03\r\xdf\x03\x03\r\xe1\x03\x05'\x9d)\xe3\x03\x03\x13\xe7\x03\x03\x13\xe9\x03\x01\x1dk\x03\x03\xb3\x1dm\t\x07\x1f%!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f'\x11\x00\x00\x00\x00\x00\x00\x00\x00#\x1b\x03\x05\xa7\xab\r\x03\x97\xa9\x1do\r\x03\x97\xad\x1dq\x1ds\x1du\r\x01#\x1d\x1dw\x13\r\x01\x1f\x07\t\x00\x00\x00\x00\x1f\x1f\x01\x13\r\x05\x07\x05\x1f\x03\x11\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x17!\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x03\x11\x00\x00\x00\x00\x00\x00\x00@\x0b\x05\x1dy\x1d{\x05\x01\x03\x03\x9f\x03\x03\xd5\x15\x03\x01\x01\x01\x03\t\x9f\xa1\xd9\xa1\x1f)\x01\x13\x05\x01\x13\x05\x05\x13\x05\t\x13\x05\r\x07\x01\x1f\x03\x11\x00\x00\x00\x00\x00\x00\xf8\x7f\x1f\x17!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f3\x11\x00\x00\x00\x00\x00\x00\x00\x00)\x05!!\t)\x01\t\x1b)\x01\x05\x0b)\x03!\t\x1d\x01)\x05!!\x05\x13)\x05!!\x0f)\x03\t\r)\x03jB\t\x11\x01\x05\x01\x0b\x11\x03\x01\x03\x01)\x03\x01\r)\x03\x02\x02\t/\t\x01\x0b\x07\x19)\x03\t\x13)\x03\x05\x13)\x03\x01\x13)\x01\x0f)\x05\x05\x05\x0f)\x03\x05\x0f)\x03!\x0f)\x03\x05\r\x04\xc6\x04\x05\x01\x11\x071\x07\x03\x01\t\r\x11\x075\x05\x035m\t\x03W\x1f\x03!\x15\x06[\x03\x01\x03\x01\x17\x07c_\x03\x01\x03\x03\x0f\x06g\x03\x01\x05\x03\x05\x05\x03\x07k\x03\x03\x03\x07-\x05\x03\x01\x03\t\x19\x06-\x03\x01\x05\x07\x0b\x1b\x07\to\x03\x01\x03\r\x1d\x07\x01s\x03#\x03\x0f\x07\x07\x01\x87\x03\x01\x03\x11\x07\x07\x01\x89\x03\x0b\x03\x11\x07\x07\x01\x8b\x03\x07\x03\x11\x07\x07\x01\x8d\x03\x19\x03\x11\x05\x03\x01#\x03\x07\x03\x07\x01\x05\x03\x07\x03\x1b\x11\x07\x01\x8f\x03+\x05\x17\x1d\x03\x07\x01\x05\x03-\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x01\x03#\x03\x07\x01\x91\x03\x15\x03!\x0b\x06\x01\x03\x01\x07'\x13%\x03\x07\x01\x05\x03/\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x0b\x03-\x03\x07\x01\x93\x031\x03+\x0b\x06\x01\x03\x0b\x071\x15/\x13\x04\x07\x05)3\r\x11\t7\x05\x03\x15+\x03\x01\x07\t\x03;\x1f\x03\x11\x05\x03\t#\x03\x07\x03\x07%\x05\x03\x11\x03\x05\x0f\x06%\x03\x11\x05\x03\x07\t\x03CA\x03\x11\x11\x07IG\x03\x15\x05\t\x0b\x05\x03\tM\x03\x03\x03\x07O\x05\x03\x01\x03\x0f\x0b\x06S\x03\x01\x07\r\x01\x11\x13\x04\t\x03\x13\x06\x03\x01\x05\x01\x00\x06\x1a}\x1f+\x11\x0f\x0b\t\t\x0b!\x7f\x1f/!!)#\x1f\x19\x0f99m\x19\x85\x89W\xb3K\x9bM\x9b\x96\x04\x1b+\x1b\x1f\x1f\x15\x1d\x15+\x83\x13\r\r\x1f\x11\x15\x1b\x17\x15\x17\x0f\x11\x15\x11+\x19)\x0f\x0b\x11builtin\x00vhlo\x00module\x00broadcast_in_dim_v1\x00constant_v1\x00get_tuple_element_v1\x00iota_v1\x00select_v1\x00func_v1\x00add_v1\x00compare_v1\x00return_v1\x00reshape_v1\x00transpose_v1\x00divide_v1\x00call_v1\x00custom_call_v1\x00value\x00index\x00sym_name\x00third_party/py/jax/experimental/jax2tf/tests/back_compat_test.py\x00broadcast_dimensions\x00arg_attrs\x00function_type\x00res_attrs\x00sym_visibility\x00iota_dimension\x00compare_type\x00comparison_direction\x00jit__lambda_\x00jit(<lambda>)/jit(main)/pjit[in_shardings=(UnspecifiedValue,) out_shardings=(UnspecifiedValue,) resource_env=None donated_invars=(False,) name=tril in_positional_semantics=(<_PositionalSemantics.GLOBAL: 1>,) out_positional_semantics=_PositionalSemantics.GLOBAL keep_unused=False inline=False]\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(8, 8) dimension=0]\x00jit(<lambda>)/jit(main)/jit(tril)/add\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(8, 8) dimension=1]\x00jit(<lambda>)/jit(main)/jit(tril)/ge\x00jit(<lambda>)/jit(main)/jit(tril)/broadcast_in_dim[shape=(8, 8) broadcast_dimensions=()]\x00jit(<lambda>)/jit(main)/jit(tril)/select_n\x00jit(<lambda>)/jit(main)/iota[dtype=float64 shape=(64,) dimension=0]\x00jit(<lambda>)/jit(main)/reshape[new_sizes=(8, 8) dimensions=None]\x00permutation\x00jit(<lambda>)/jit(main)/transpose[permutation=(1, 0)]\x00jit(<lambda>)/jit(main)/add\x00jit(<lambda>)/jit(main)/div\x00callee\x00api_version\x00backend_config\x00call_target_name\x00called_computations\x00has_side_effect\x00operand_layouts\x00output_operand_aliases\x00result_layouts\x00jit(<lambda>)/jit(main)/eigh[lower=True sort_eigenvalues=True]\x00jax.result_info\x00tril\x00[0]\x00[1]\x00main\x00public\x00private\x00\x01\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x08\x00\x00\x00M\x08\x00\x00\x00cusolver_syevj\x00",
        xla_call_module_version=4,
    ),  # End paste

    # Pasted from the test output (see back_compat_test.py module docstring)
    f64_syevd=dict(
        testdata_version=1,
        platform='cuda',
        custom_call_targets=['cusolver_syevd'],
        serialized_date=datetime.date(2023, 3, 17),
        inputs=(),
        expected_outputs=(array([[-3.1486359056225782e-01,  3.7431364158123925e-02,
            6.1831284766658730e-02, -1.2946991231313536e-02,
            1.9330566993707950e-02,  3.1760201896488226e-03,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            0.0000000000000000e+00,  0.0000000000000000e+00,
            9.4213470166864710e-01, -8.6414847942068732e-02],
          [-2.9939200325938797e-01,  8.3501568928299474e-01,
            4.0680107296867257e-01, -4.6573192775473518e-02,
            6.5422207600829785e-02,  2.2099527094683900e-02,
            -1.0242349878775975e-02,  4.0829390183091318e-03,
            -1.5827725558444371e-02, -8.6793932713605769e-03,
            1.3047005177451432e-03, -5.3573283556152184e-03,
            -1.1723085990292578e-02, -3.4282481604778923e-03,
            1.5300655388654032e-03,  1.3010433879291027e-02,
            -7.6245808434662662e-03,  5.9569775610370131e-04,
            -5.9294293157650772e-03, -1.9734040942842074e-03,
            -1.8628968192927392e-02, -1.3034235399858809e-02,
            -5.0097004610369401e-03,  2.4749245795903537e-02,
            -5.0644358547264675e-03,  3.0532167800601515e-03,
            2.0824661626164857e-02, -1.5147462161617094e-03,
            1.6322395782111299e-02, -1.1236053191734820e-02,
            -1.1821960842042806e-02,  3.8822577430670320e-03,
            7.0724820528586508e-04,  1.9906723944256747e-02,
            -1.7030338737863057e-01, -9.0661051391036640e-02],
          [-2.8392041595652112e-01, -1.0171687781151459e-01,
            -1.1816431661072314e-01,  2.9212172394267638e-01,
            3.3294458108354380e-01,  4.2087881292542445e-01,
            -2.2194306321456944e-01,  1.2056157631930936e-01,
            -1.0764065526585581e-01,  4.4945129933377570e-02,
            -1.1518299700192679e-01, -3.1085391640205563e-02,
            3.1385765542768805e-02, -2.2533661915179113e-02,
            9.3053311217867085e-02, -1.6099650538834706e-01,
            -3.8639305088265900e-02,  9.2990366329018387e-03,
            4.6666113341746911e-02, -2.1871647987757620e-01,
            1.7703518610745730e-01,  1.5467613762024190e-01,
            -7.2294521250116733e-02,  2.3499877830015681e-01,
            -5.6829378083033165e-03, -1.0178485446351725e-01,
            1.7877785721217213e-01,  2.1684187554288339e-01,
            7.7233872499541889e-02,  2.2835265304748494e-02,
            3.1080805156356406e-01,  3.1722234078538948e-02,
            -7.8092425763001377e-02,  9.4554636051152510e-02,
            -9.6031463624110386e-02, -9.4907254840003452e-02],
          [-2.6844882865365438e-01, -2.0201860535424061e-02,
            -2.0343029420688158e-01,  1.2815855886454322e-01,
            4.8774092445450092e-02,  1.3232562034943543e-01,
            -1.8521836621459195e-01,  9.8747816539597660e-02,
            2.7324903486606195e-01, -7.8737437097193080e-02,
            4.9421661772677816e-02,  7.1493931251323112e-02,
            3.5542595611320515e-01,  1.3920746216059152e-01,
            -2.8249741974519734e-02,  6.7932896387190703e-02,
            -2.3008512044551552e-01,  5.5015746716542496e-02,
            -6.0329018554125865e-03,  8.4249901371007491e-02,
            -1.0850059549176212e-01, -2.7052679792044718e-02,
            1.7199248671821082e-01, -2.0779039909219962e-01,
            1.1023999772580403e-01,  4.0228126834019268e-01,
            -7.1331569093078903e-02, -2.2546040356632324e-01,
            -5.6848723613690040e-02,  2.0039103669806510e-01,
            -2.2375524112669190e-01, -6.6955463229343037e-02,
            -1.4356710092268696e-01,  2.2907198003730800e-01,
            -8.4342913246148038e-02, -9.9153458288970819e-02],
          [-2.5297724135078736e-01, -9.7633470097019753e-02,
            -2.0613664461051402e-02, -4.6575018452204114e-01,
            -4.5475545929408095e-01, -1.6835202228307944e-01,
            -2.7411043542686481e-01,  1.4382896244553764e-01,
            1.5533482960243880e-01, -7.7897907011887785e-02,
            -5.9104799414908579e-02, -5.1049057176047449e-02,
            5.0937034273965797e-03, -2.9920502980456239e-02,
            7.9164430071644656e-02,  6.5334090456028976e-02,
            -2.4594170101813598e-01,  4.0287932953704184e-02,
            1.3071075582032446e-01, -5.6912271071735306e-02,
            -1.2680756132856946e-02,  3.5044366466197449e-02,
            -5.1780762628180410e-03,  1.2325979893038844e-01,
            -1.3286387357961091e-01, -1.9718715617446650e-01,
            -7.0204376770955132e-02, -9.3710658292701816e-03,
            7.6870928390159760e-03,  1.2623341382152653e-01,
            3.4895566103640097e-01,  7.7553659039143241e-02,
            -3.4023999296528072e-02,  8.3074702907895745e-02,
            -8.5300072672481381e-02, -1.0339966173793817e-01],
          [-2.3750565404792034e-01, -8.2181485614283623e-02,
            -2.4796576412755008e-02,  2.6469606244089910e-01,
            2.5136155191565374e-01, -8.5932117879471037e-01,
            -6.7801327364868255e-02,  2.3630380146045637e-02,
            -6.0339530364635997e-02,  2.4318784991642788e-02,
            -2.0157980609574723e-02,  1.3969684905577337e-02,
            5.2064373452097072e-02, -1.3504287072787914e-03,
            1.1948855400414819e-02, -7.7684684576308824e-02,
            -1.8126869586737940e-02, -3.2895203661275497e-02,
            -4.7194795185232655e-03, -6.2526420481870917e-02,
            7.8353014950393762e-02,  4.3021669650274826e-02,
            4.1123834759705602e-02,  2.1527669096626890e-02,
            3.2298969317449348e-02,  2.3438124417394162e-02,
            3.1518151219115144e-02,  8.9704214482948422e-02,
            7.6821260017619769e-03, -8.5409778343425186e-03,
            1.5521001031338759e-02, -1.3290428648657086e-02,
            1.8906628930454021e-02, -1.2782589525387992e-02,
            -8.2979044248598546e-02, -1.0764586518690553e-01],
          [-2.2203406674505338e-01, -9.0264475102341105e-02,
            9.0740700176499111e-03,  6.9171384437416147e-02,
            -1.3111811612891669e-01, -1.8966507957248607e-02,
            4.0414304307463594e-01, -3.2564666059313241e-02,
            5.6086124244845181e-01, -4.0083205571491060e-02,
            -2.4505702319715772e-02,  2.8981348567837486e-02,
            -1.8028953963325864e-01,  1.2810669493073431e-02,
            -3.0205734928244080e-02,  1.3016546116209483e-03,
            4.1180187675978214e-01,  1.8487430939971340e-03,
            2.1878399115523185e-02, -1.2942737544986772e-02,
            3.1612876215063763e-02,  1.9040590265843902e-02,
            -2.9853451951736565e-01, -2.1069261774264141e-02,
            1.2756924052704141e-02,  1.0396556130345047e-02,
            2.0982593071380967e-01,  7.2513245350085284e-02,
            2.6961322653924678e-02,  4.4259057451694346e-02,
            1.3245555422671054e-02, -1.1355432725780245e-02,
            -1.6423769454471046e-01,  2.1283797622603673e-01,
            -7.7771821344734746e-02, -1.1189206863587289e-01],
          [-2.0656247944218639e-01, -7.5555152047925872e-02,
            2.1436004480934572e-02,  1.8519822533150174e-01,
            -4.7687267679858099e-02,  1.0893715640778658e-01,
            5.4446388557811642e-01,  6.7864355635107079e-02,
            1.8925675037139755e-01,  3.6392773516755073e-02,
            -2.4764455183159433e-02, -3.8468294614801751e-02,
            -2.8696444635530814e-02, -1.8823021866307067e-02,
            4.8264052464878845e-02, -3.6882747079153497e-02,
            -3.0155420938729255e-01,  1.0404831951207047e-02,
            4.4505477004053171e-03, -4.6873846610364103e-02,
            2.4798470273412251e-02,  2.5891733287640804e-02,
            3.5011544817152707e-01,  8.5903050378751358e-02,
            -1.6860450574909990e-02, -3.9052038500091160e-02,
            -2.9924661599529656e-01, -1.5823886416275893e-03,
            2.8254484941419005e-03, -4.8861168063938747e-03,
            9.7917302635802658e-02,  2.7710576047465570e-02,
            2.3536560145276611e-01, -3.9600571986552502e-01,
            -7.4934893198527877e-02, -1.1613827208484023e-01],
          [-1.9109089213931946e-01, -8.4666472598656825e-02,
            5.7740802097843921e-02,  1.9626130737187028e-01,
            -2.4601756649487860e-01,  8.1511271167717628e-02,
            -4.6530930078469529e-01,  6.8795587726048116e-02,
            5.2415554010200038e-02, -1.7332120317563506e-03,
            3.1251731285109323e-02,  1.5521676381926154e-02,
            -1.2359815126908288e-01,  2.7460289856811461e-02,
            1.9114633014954776e-02,  2.8966001347205911e-03,
            4.3487864890462036e-01, -2.2957986155413699e-02,
            -1.5357935266312277e-02,  1.0016152245695723e-02,
            -4.5019081491420573e-02, -2.4405778384030734e-02,
            -7.4832588748429490e-02, -4.4078616914614753e-02,
            3.0809052034342380e-02,  1.1926634983737788e-01,
            -8.1517751909305367e-02, -7.7527914203627396e-02,
            -3.7123430398910418e-02,  1.3750979135916276e-02,
            -9.7457414231716055e-02, -1.7178991628521816e-02,
            2.1304973749867503e-01, -5.4941011823140218e-01,
            -6.7860578570392335e-02, -1.2038447553380759e-01],
          [-1.7561930483645249e-01, -8.8342789136092309e-02,
            -1.1242590243640400e-02, -1.8652768797207359e-01,
            -9.8464009205703876e-02,  1.7256713195193910e-02,
            2.9649268724224581e-01,  5.8780632678962143e-02,
            -3.4585362321307522e-01,  7.6907763800451081e-03,
            2.5103268120083535e-02,  2.5393826053803564e-02,
            4.3240349879996420e-01,  3.3310696488693933e-02,
            2.1609140330890370e-02,  1.3951456173138647e-03,
            -1.2840968480253712e-01, -3.3248191939129826e-02,
            -8.9379099725266672e-04, -1.8994911138723630e-03,
            -2.3834826680311980e-02,  4.7502947323282011e-03,
            -4.4024121870114297e-01, -6.7327999197165686e-02,
            2.9359383382924452e-02,  9.1479482958182867e-02,
            3.8593300484440007e-01, -4.7958512765110956e-02,
            -5.1251961259242168e-02,  1.8636628882937378e-02,
            -6.5572564769060912e-02, -2.2887842635462220e-02,
            -1.6042006104302377e-02, -3.3250776465128573e-01,
            -6.6477273291217359e-02, -1.2463067898277495e-01],
          [-1.6014771753358550e-01, -8.3434053708109190e-02,
            1.3638599925185501e-02, -2.4158649874087133e-02,
            -1.1124755841847851e-01,  4.2695267715302458e-02,
            1.4866152720116035e-01,  4.9700778378845270e-04,
            -3.5326388070491549e-01, -1.5745483283003094e-02,
            -8.9738221678782072e-03,  1.0993364411347295e-02,
            1.9527915544397639e-01,  1.3259513825918660e-02,
            -3.9339417079053149e-03, -3.7389315402467350e-02,
            3.0825337281314197e-01,  2.9465425388143118e-02,
            -1.0086552608467406e-04, -2.1130010935818223e-02,
            2.4746795171351338e-02,  1.2876294127766924e-02,
            -1.3542161100061775e-01,  2.3491306500478031e-02,
            2.8381089185132442e-02,  5.0060402655999779e-02,
            -4.7990645387633185e-01,  1.7841388064942280e-02,
            3.6163722246352295e-02,  2.2692968040711251e-02,
            -1.4881297657765719e-03, -1.1068249840362020e-02,
            4.3250260717661632e-01,  4.5393847466427317e-01,
            -6.1116215809998306e-02, -1.2887688243174231e-01],
          [-1.4467613023071851e-01, -8.5360329958689612e-02,
            3.6773895176301370e-02,  2.8417567832807769e-04,
            -1.4251569175101705e-01,  1.8419541161364662e-02,
            1.4739729008583152e-01, -6.2901931512317516e-02,
            -4.3820330673251112e-01, -1.1585923923104585e-01,
            -4.6526417840431711e-02,  1.2161556905396271e-02,
            -8.3388018002128958e-02,  2.3616237126461999e-02,
            -9.1086898933490409e-02,  9.6073985629915787e-02,
            3.0200810799555788e-01,  9.9080289536070815e-02,
            4.9921034650103280e-02,  7.6871969202905246e-02,
            -8.3377720121475072e-03, -1.7031625806123534e-02,
            4.5636496936456672e-01, -4.0005637071420394e-02,
            -1.9891703100641429e-02,  1.2472945837760744e-02,
            5.9697784009368959e-03, -9.5789228620796370e-03,
            6.8806967828826657e-02,  1.5038487697273856e-01,
            6.8452882565985446e-02,  1.3123694381544091e-02,
            -5.6226049096551989e-01, -4.1018946243773058e-02,
            -5.6717572380307106e-02, -1.3312308588070965e-01],
          [-1.2920454292785150e-01, -6.6253352907543861e-02,
            -1.0164436321011842e-01, -1.4433060335444364e-01,
            1.6028176487458967e-01,  3.4584483531135940e-02,
            1.9900533500768001e-02, -5.2164178106233798e-02,
            -1.2875710620386896e-01, -1.3038955529948765e-01,
            -3.1311992664378889e-02,  2.5299917094429910e-02,
            -4.1764341929454979e-01,  5.7547077142788963e-02,
            -1.1598534347679475e-01,  1.8086109486937549e-01,
            -6.3115663671148348e-02,  8.6408791666891471e-02,
            4.0289642159952954e-02,  1.2892059198986330e-01,
            -7.5052803928986972e-02, -3.4807004039357006e-02,
            2.0072216849958635e-01, -1.1909118683716058e-01,
            -2.6393566026650855e-02,  6.6849035713186178e-02,
            4.7200759534307635e-01, -7.6853961442131774e-02,
            2.6993333821331650e-02,  1.7484304402685918e-01,
            5.3240433359001025e-03,  2.9788042206222785e-03,
            5.1760936987899087e-01,  1.1384037033693235e-01,
            -5.1865856323749862e-02, -1.3736928932967699e-01],
          [-1.1373295562498452e-01, -5.7235135967154585e-02,
            -4.7652965020097103e-02, -1.7627396739100985e-02,
            7.7938405922626644e-02,  2.2087656281477019e-02,
            6.1009605667557178e-03, -5.4981966965685393e-02,
            -1.8486086378646865e-01,  3.8911039431433647e-02,
            3.5079519080830110e-02,  1.9272432328556483e-02,
            -5.9096451891695889e-01, -7.7247905448605157e-03,
            3.7441325666613741e-02, -4.9165769090891341e-02,
            -3.3776276260195798e-01,  1.6606308621317768e-02,
            3.8859102913090936e-02, -1.9047412918711374e-02,
            -3.8482634352387676e-02, -4.8755071639337150e-02,
            -4.3270527443011519e-01, -9.1999354995766322e-02,
            1.0430914529054176e-01,  1.4978760949122619e-01,
            -3.4135100214765429e-01, -2.5289826614278744e-02,
            3.4608873349492607e-02,  8.8085003662463843e-02,
            -1.5196825642675141e-01, -9.3051296574294673e-03,
            -2.4468277187262805e-01, -2.4348157193486621e-02,
            -4.7513567722300747e-02, -1.4161549277864433e-01],
          [-9.8261368322117570e-02, -1.6390394385331745e-02,
            -5.4742294041798749e-02, -5.8987021949670405e-02,
            -1.6882319276059432e-01,  4.3601612172208745e-02,
            -2.9911314975774938e-02,  2.3284677199386728e-03,
            -3.1808540586289284e-02,  6.9627318822466044e-01,
            1.6271702602637766e-01,  1.5743246880124597e-02,
            -4.3195703838658110e-02, -2.2494758789598773e-01,
            7.1399213422553218e-02, -1.3240943946997921e-01,
            -8.4980139589052577e-03, -3.2038201094679952e-01,
            6.2407097431780204e-02, -7.6882180114861851e-02,
            2.9470860002467913e-02, -4.2571478756212582e-02,
            2.0163350380724604e-01, -3.2389702717405428e-01,
            6.9711204990479309e-02, -8.1573794801329258e-02,
            1.3304500243627673e-01,  4.0406118875997113e-02,
            8.2477981782237836e-02, -1.1543529624088469e-01,
            -1.1014206710642817e-01,  4.2320022953069426e-06,
            3.8041226304310447e-03,  1.3395530894194055e-01,
            -3.9467794046677329e-02, -1.4586169622761166e-01],
          [-8.2789781019250525e-02, -1.9278711714630567e-01,
            2.2165755909431184e-01, -2.1201546316262262e-01,
            1.4307796989725635e-01,  6.0334342472999250e-02,
            -5.5139304406736672e-02, -1.9408969113742302e-02,
            5.4970843704949646e-02, -4.5047658482968128e-01,
            -3.3338315762977556e-02, -6.5308425743183532e-02,
            1.4218465309675436e-02,  4.9087218418760230e-02,
            1.8670840217742501e-01, -1.5287462038432642e-01,
            -1.3217180940167689e-02, -6.6463048958420534e-02,
            3.8845065361654303e-04, -2.2429929685530131e-01,
            -2.6776933696982124e-02,  8.5772405898653856e-02,
            1.1857225379472448e-01, -3.3789334871471582e-01,
            8.3834684881833613e-02, -1.7391265231974168e-01,
            -5.9431721332300208e-03,  2.7485104738181495e-02,
            1.6105963634532708e-01, -4.7246605597344127e-01,
            -2.3898285645951292e-01, -2.0628986543330220e-02,
            -2.1798010578591574e-02,  1.6076906598537423e-02,
            -5.4377032852269684e-02, -1.5010789967657906e-01],
          [-6.7318193716383562e-02, -1.3247564302860890e-01,
            1.7006921492087917e-01,  1.2398760160260749e-01,
            -1.4177630269484331e-01,  1.5422349385403381e-02,
            -5.9592326716797428e-02, -3.5882053764316857e-02,
            -1.7232432793461348e-02,  2.3701488719579314e-01,
            -4.6593215018650616e-02, -6.3082282004145299e-02,
            -2.0902723950643357e-02,  5.2050993065408405e-02,
            -8.0468326155430828e-02, -5.0880717820819980e-02,
            -1.1820152914284968e-01,  5.6506976812092713e-01,
            -2.1968735055254530e-02,  1.6529598718631755e-01,
            1.0797738052990204e-01, -3.0113303079001008e-02,
            5.5521405735639642e-03,  2.7802427161516047e-01,
            -1.3829193596041753e-01, -1.1466435184415830e-01,
            1.1740546330296046e-01, -1.7311150238082029e-01,
            -1.6365530586101310e-01, -3.6819727396673907e-01,
            -3.1239015782869367e-01,  6.3966770007709506e-02,
            -2.6591619532336051e-02,  1.2885889151522636e-01,
            -3.7992961598361283e-02, -1.5435410312554640e-01],
          [-5.1846606413516585e-02, -6.0477319044140554e-02,
            -7.5750638182608219e-03, -1.0624372654415394e-01,
            8.1266486795481985e-02,  4.0180836057036554e-02,
            -3.7783670829837974e-02,  4.6289675320758547e-02,
            3.3808855820936547e-02, -1.9195948450068509e-01,
            -5.8196442046703094e-02,  1.7282080569685822e-03,
            1.4755965059760449e-02, -6.0959969133142022e-01,
            -2.8239274796445768e-01,  1.2486767782495350e-01,
            -1.6812624118941352e-02, -3.1637047991210354e-01,
            -3.4329518102613220e-02,  2.9658523886210797e-01,
            2.1095830387260842e-01, -7.1581690223787436e-02,
            1.4902746008909057e-02,  2.5118050689616306e-01,
            1.5960904763919231e-01,  1.6146826320314336e-01,
            -3.0778528162015331e-02, -6.0781897242040703e-03,
            -1.5766062756371724e-01, -2.2924930849571712e-01,
            -2.3919944196342770e-02,  4.0432828090792343e-02,
            -3.3603315710298294e-02,  6.6005717038430623e-03,
            -3.2237412023528290e-02, -1.5860030657451374e-01],
          [-3.6375019110649567e-02, -4.6095054123273631e-02,
            4.1487329226456366e-03, -4.9882330119267008e-02,
            2.6789583798631911e-01,  2.8310263556813459e-02,
            -5.0744234427433435e-02, -2.1955670997388516e-01,
            8.8814242427478526e-02,  7.2616405945027329e-02,
            3.7105581486243189e-01,  1.3801726499993164e-01,
            1.2228306569610396e-01, -1.8641957679946289e-01,
            -1.7746951776518829e-01,  1.1838468893129621e-01,
            4.1434840944853890e-02,  3.4352445701196649e-01,
            -1.3539286248067484e-01,  1.2179016223131671e-01,
            -1.4481862254120659e-01, -6.0813770391397334e-02,
            -9.5024877677197070e-02, -2.6026144416788322e-01,
            6.7007386100264313e-02, -2.7403316717453452e-01,
            -1.2940472617950355e-01, -7.0811325772559455e-02,
            1.0283464270665656e-02, -5.0042226650144100e-02,
            3.9567119578457077e-01, -2.3131183910318670e-01,
            -2.4438157021422158e-02, -9.5495078814865603e-02,
            -3.1811761848109070e-02, -1.6284651002348108e-01],
          [-2.0903431807782615e-02,  7.2327502897265056e-02,
            -2.1426834420397733e-01, -2.4971807305411563e-02,
            -6.8251303361485452e-02, -3.5176957926268708e-03,
            -1.7281098595222758e-02, -2.7919893499292525e-01,
            -7.5490419998562163e-03,  8.8933532299955390e-02,
            -8.3918077552881970e-02,  4.2946166228858822e-02,
            -3.5084337029511685e-02,  5.2484778345047800e-01,
            -1.3476341073870199e-01,  8.9651093734304757e-02,
            -2.6221874920893444e-02, -3.2081171793188057e-01,
            -7.0201683149374666e-02,  9.7920337768921742e-02,
            -7.6208072805887969e-02,  2.9964575931518713e-02,
            2.1839138515231137e-03,  2.1907625163481245e-01,
            7.8802565386018458e-02,  1.0637722019900711e-01,
            -1.5047419808766808e-02, -1.2522929609505140e-01,
            1.0489044814827699e-01, -4.4452472469644072e-01,
            2.5261973738582033e-01, -1.9360753077714768e-01,
            -3.0637038971187570e-02, -3.9473390838082588e-04,
            -1.0054456334322568e-02, -1.6709271347244839e-01],
          [-5.4318445049156196e-03, -1.1991560506989501e-01,
            1.6016393502783463e-01, -9.0534713898102900e-02,
            1.7803986653673967e-01,  4.2517830558630100e-02,
            -6.5595472901773699e-02, -6.9456352075150884e-02,
            7.9849581869208763e-02,  1.4596149872374808e-01,
            -3.7448911148165226e-01,  3.0784697110174092e-02,
            1.0212691273921030e-01,  1.2477201433959939e-01,
            -2.1170895978207616e-01,  1.9057503902571590e-01,
            -1.9885301263116554e-02, -2.1847437899940467e-01,
            -1.3659628076825936e-01,  6.2262165446311392e-02,
            -1.9622860693073528e-02,  4.1620399347292121e-02,
            -3.1648999142503326e-02,  8.2027519954154221e-02,
            -7.9260224219164649e-02, -4.4257777757196498e-01,
            -1.0450524222584731e-01,  7.1670676847096298e-02,
            4.6620848245388563e-02,  3.5490360494088574e-01,
            -3.4694381436297000e-01, -2.2966638374036538e-01,
            -2.1349097951285249e-02, -5.0149218417714851e-02,
            -2.8318514185483656e-02, -1.7133891692141581e-01],
          [ 1.0039742797951326e-02,  1.4486958501002600e-01,
            -3.0487486722127227e-01,  1.2108072885929126e-01,
            -1.1723298949673400e-01,  9.6017523703054095e-03,
            4.9883113678426960e-03,  3.2018649396693973e-02,
            -4.0095882258820964e-02, -2.4528012104090294e-01,
            6.0349817604330003e-01, -6.0025406492642708e-02,
            -1.6146280657180472e-02,  1.5798023347451132e-01,
            -1.5035528625979958e-02, -2.2434556029665070e-02,
            -2.4354754626807390e-02, -1.5308774844201870e-01,
            -1.1065734099847921e-02,  5.1339996940509787e-02,
            1.6396255893983677e-01,  2.4722965810338692e-02,
            9.6017297101513074e-03,  1.6662850312888863e-01,
            9.1395453034799151e-02, -4.2004786665153609e-01,
            3.0226599593042958e-02,  3.3204444593892296e-02,
            -9.0545811500522586e-02,  1.1327046229049616e-01,
            -2.5108979165208944e-01,  1.2687846708619716e-01,
            -2.1404901679780933e-03,  2.9977168343317158e-02,
            5.5400172108409033e-03, -1.7558512037038310e-01],
          [ 2.5511330100818325e-02, -5.8698168696025753e-02,
            8.0629703301508024e-02, -7.0612253616157819e-02,
            3.2715731475630602e-02,  2.1732269341780134e-02,
            -5.6700795470449199e-02, -6.8235752853351661e-01,
            6.4905178300795938e-02, -3.5862976828251472e-02,
            8.8618413873728166e-02,  3.1550620324006268e-01,
            9.2319437517647415e-02, -1.0599662867975553e-01,
            2.6587503059973538e-01, -1.0545080566473539e-01,
            -2.2738440485640277e-02, -6.6368929276419075e-02,
            -5.1003071286368440e-02, -1.1626185301232636e-01,
            5.4119363471023328e-02, -2.4882466696968256e-02,
            4.6420092314024886e-02,  1.7831888983094824e-01,
            -2.7253935859206135e-01,  1.7198911112035339e-01,
            1.3432430343834192e-02,  7.1000954309573148e-03,
            -3.8416339301886476e-03,  1.6384316059667964e-01,
            -6.0953258543061287e-02,  2.6960776094017469e-01,
            2.0718992188831518e-02, -2.7614704623654989e-02,
            -1.2643038301898243e-02, -1.7983132381935049e-01],
          [ 4.0982917403685273e-02, -4.7160343894475723e-02,
            7.8787266856851345e-03, -1.6730572778497552e-01,
            2.7113248408711793e-01,  9.8438763801876154e-03,
            2.2608843153598773e-02,  4.0738411310515976e-01,
            3.2355058682223534e-02,  1.1698920368317291e-01,
            1.4072643414054364e-01,  6.7061453574130916e-02,
            -1.8930127519950827e-02,  1.9146087806398635e-01,
            -2.4250669817151019e-02,  1.1868698006794093e-01,
            1.0317141879348907e-01, -8.5252634874863287e-02,
            -2.8010523433118828e-01,  1.3060583612270180e-01,
            -9.9969111180962050e-02, -3.4760563118607063e-02,
            -1.7994529116745678e-02, -6.0554676763009442e-02,
            -4.6559703882739706e-01,  1.1940676107160293e-01,
            -1.0161278374127546e-01,  1.3173327834920193e-01,
            -2.2709272071986680e-02, -1.1755702148341549e-01,
            3.7441059930431703e-02,  4.4164660080364565e-01,
            -6.6992110689447992e-02, -2.5301348191003502e-02,
            -9.7262032302421250e-03, -1.8407752726831786e-01],
          [ 5.6454504706552257e-02,  7.8158541336176779e-02,
            -1.4338657014458589e-01,  1.0703741291078765e-01,
            -1.3942580377761906e-03,  2.2695174951015635e-03,
            -3.8562621975632518e-02, -3.0965063003047144e-01,
            3.7355997032764349e-02,  1.4990453152525209e-02,
            -1.1227058245216649e-01, -7.0287795373175999e-01,
            1.1718292741895955e-01, -5.1035967037226390e-02,
            -9.4000621055494157e-02,  1.7518267045374700e-01,
            -1.4730348981690847e-02,  5.1783743616797537e-02,
            2.1169018058168132e-01,  5.8597372997689870e-02,
            -1.6243455966644404e-01,  5.9497378897041750e-02,
            -7.3121464646455983e-02, -1.8084067697810838e-01,
            -6.6501694611624321e-02,  4.1097079298917809e-02,
            -4.3356588698331838e-02,  2.4444891440205574e-01,
            6.5642952335239826e-03, -9.6906979426258765e-03,
            1.8913630981055121e-03,  2.7008769602574367e-01,
            8.8545125037905337e-03, -3.9988001886776758e-02,
            9.3906452280477001e-03, -1.8832373071728517e-01],
          [ 7.1926092009419212e-02,  8.0994217906793439e-02,
            -2.0767188447365928e-01, -1.5196436606475891e-01,
            1.3077919554196207e-01, -2.1254474743086713e-02,
            4.5019671597743463e-02,  9.6558458919928689e-02,
            1.2420216348711157e-02, -6.2064238471275191e-03,
            9.8956490118614168e-02, -3.2363738790615754e-01,
            -3.2870638207842147e-02, -1.5482218310094722e-01,
            2.9647782998980127e-01, -6.1576762109174010e-02,
            1.2666434428081200e-01,  2.1955834692424955e-02,
            -1.8997255642944891e-03, -1.0295835477975461e-01,
            1.8208445909004639e-02, -1.1030261882048981e-01,
            4.3794875217006007e-02,  1.8518198489376456e-01,
            -4.0747443172392700e-01,  1.3827664021164707e-01,
            -4.1431123873109715e-03, -1.4061023435938111e-01,
            1.3942741953117222e-02,  1.9365617058920072e-02,
            -8.4489815015323350e-02, -5.7838799828344145e-01,
            -2.8902818751484066e-02, -2.4186610549109096e-02,
            1.2086263962861131e-02, -1.9256993416625251e-01],
          [ 8.7397679312286217e-02,  1.5064561887342939e-01,
            -2.1080556782941462e-01,  1.5916760566958116e-01,
            -1.9624826757584166e-01,  1.5198104896205650e-02,
            -1.4330248064956560e-02,  3.3068118190946301e-02,
            -3.5714352226646290e-02, -1.4260141979380403e-01,
            -2.4115477092387741e-01,  3.4101861982281523e-01,
            -1.9029646752241479e-03, -2.7699284020832545e-02,
            1.0920088465260440e-01, -1.5239532632222408e-01,
            -8.5144012779746134e-02,  5.5970342531910411e-02,
            6.9106614215268647e-02,  2.4036876137100174e-01,
            -1.2301443222654272e-01, -1.1953863304856910e-01,
            3.5171852820881193e-03, -2.1104179481631563e-01,
            -1.6652675336533382e-01, -6.9825511877400867e-02,
            7.3611503187800218e-03,  5.1349708686040763e-01,
            -3.0172148431909446e-01, -1.0589893886410634e-01,
            3.6783462028334960e-03, -2.0553003985674112e-01,
            1.8790472746182015e-02,  1.9823557204917654e-02,
            2.5168461511062466e-02, -1.9681613761521988e-01],
          [ 1.0286926661515323e-01, -5.1095768728277327e-02,
            1.3471859461003702e-01,  3.0500821091821676e-02,
            -1.6790235354550213e-02, -7.0308669455806175e-03,
            -3.0939649438101019e-03,  2.5665199177927620e-02,
            2.1279168221811904e-03, -2.5037640808915945e-02,
            -1.2405085129935786e-01, -2.6231150724568519e-01,
            -8.5787446133464614e-03,  3.9627338244596369e-02,
            2.3267441336286346e-01, -4.0743293242468487e-01,
            2.4149661576382757e-04, -6.3680910375172040e-02,
            -4.3805185403053759e-01,  2.0300111728111647e-01,
            -2.1099142295899803e-01, -3.4325637130492054e-01,
            2.4798870388207689e-02,  5.8652422232119368e-02,
            3.1273508409742873e-01, -6.5663309732651248e-02,
            8.4976320234436575e-02, -1.2972698624062320e-01,
            -1.0136590956706468e-01,  1.7606369902531008e-01,
            1.7776135567204221e-01,  1.0742707779456324e-01,
            -7.9052346006256245e-03,  7.3493627583932908e-02,
            9.9131943085618447e-03, -2.0106234106418724e-01],
          [ 1.1834085391802023e-01,  8.1061946874736585e-02,
            -1.6265342280467382e-01, -2.5856375159094996e-01,
            1.4258244531423583e-01, -2.5799424990869069e-02,
            1.9638649342146815e-02,  7.3355921016709083e-02,
            5.9394009978013036e-02,  1.5655633426552953e-01,
            -9.8792934500238835e-02,  9.9575902680803088e-02,
            1.8527367488061958e-02,  6.3288806058580380e-02,
            3.7739330071097632e-01,  3.9157302813010320e-01,
            1.3485974151563190e-01,  2.4396726581112591e-01,
            3.6171829433890815e-02, -1.5329124928290030e-01,
            1.0994295285071572e-01, -6.2470988682208468e-02,
            7.2649015124010521e-02,  1.4656583051512045e-01,
            5.0160574613932607e-01,  4.7267639935224738e-02,
            -4.2965682291764895e-02,  1.8881658695211850e-01,
            -1.0776584277343945e-01, -2.6754374009298049e-02,
            -7.7009726198669998e-02,  8.6417047403639091e-02,
            -5.3833621971674586e-03, -8.0918819205681225e-02,
            2.1780800232539175e-02, -2.0530854451315456e-01],
          [ 1.3381244122088717e-01,  1.5997082437941978e-01,
            -2.1906335649966574e-01,  2.3332171765159351e-01,
            -6.4994730069703827e-02, -2.7137179321886296e-02,
            4.4299490835366419e-02,  5.4082161016101568e-02,
            -6.1822856454263338e-02, -6.6517101749567792e-02,
            -2.9376460130324589e-01,  1.1103413626514062e-01,
            -3.3806550575053815e-02, -1.8397686746205080e-01,
            3.9400318507963744e-02,  1.8758272608343995e-01,
            2.1898570040268548e-02, -5.7258401311702969e-02,
            -1.2054652895121606e-01, -3.3785342949153890e-01,
            -3.9112933378476634e-02,  1.2987622324621689e-01,
            -7.4850924489854642e-02, -1.8237325410753219e-01,
            -1.1058781873480500e-01, -2.0595217802395629e-01,
            7.5757742040963461e-03, -5.3655875317100610e-01,
            -2.0896258914648322e-01, -5.5945308120122161e-02,
            8.2455318541596961e-02,  1.7624602710846482e-01,
            -2.2489297400574856e-02,  5.2915934277181324e-02,
            3.8152138968863464e-02, -2.0955474796212192e-01],
          [ 1.4928402852375416e-01, -4.7103999084602964e-02,
            1.5843017378423407e-01, -1.0471529213101267e-01,
            4.1822224430947852e-02,  4.9674575956627585e-03,
            -1.3311898606966285e-03,  4.8322275176183468e-02,
            2.6782623911085109e-02,  1.3647784270166637e-02,
            1.0980857986376788e-01, -5.0748588072257886e-04,
            -1.0361251293227987e-02,  1.1049141088458188e-01,
            -4.7174567274205670e-01, -2.0220954115377396e-01,
            1.3182956708179594e-02, -1.1843903142333311e-02,
            2.0088578029524848e-01, -5.3080319758187777e-01,
            -1.6308626968204651e-01, -1.6901681485606096e-01,
            7.0269705034495436e-02,  9.8708103667137601e-02,
            5.8906260202682963e-02,  1.3406466835766842e-01,
            1.3927440769859889e-02,  9.2483635015958410e-02,
            -4.1489874017913597e-01,  5.3520424215223954e-02,
            3.3087563030626183e-02, -4.3491644319790287e-02,
            -1.4259433018598195e-05,  8.4993306168228474e-03,
            1.9440725644047020e-02, -2.1380095141108932e-01],
          [ 1.6475561582662113e-01,  1.5611161975472390e-01,
            -2.2981567498448408e-01, -2.5170242091030143e-01,
            1.2572164509633985e-01, -3.5101394036068920e-02,
            1.4388788465769620e-02,  6.4367254285863956e-02,
            7.9127393952476463e-02,  4.7770236979792664e-02,
            -1.4967962998375717e-01,  9.6657597995555136e-02,
            2.8600846275685401e-02,  1.0247100377903102e-03,
            -1.7416826445456965e-01, -5.2903452729155642e-01,
            1.2378709088794008e-01,  1.6002483124980629e-01,
            2.3117191956384286e-01,  2.0936710152049257e-01,
            8.2739337123958492e-02,  3.7851995698789648e-01,
            1.9060641335918893e-02, -6.4314540668445608e-03,
            8.4778867125413743e-02,  1.6232730574310308e-02,
            -5.8776952506303194e-02, -1.6317833767006093e-01,
            2.0541131812472332e-01,  9.4709191370766388e-02,
            -3.0776520624173034e-02,  1.1938827858311649e-01,
            1.2517716200189802e-02, -1.3352132837280375e-01,
            3.8021934168930759e-02, -2.1804715486005660e-01],
          [ 1.8022720312948814e-01,  8.6827318631575279e-02,
            -7.4501227114099414e-02,  1.2876209736226957e-01,
            -2.2037890384696301e-01, -2.5814842105572621e-02,
            -3.1406758090893994e-02,  9.6294241223690305e-02,
            1.8240072112824506e-02, -7.8775576899911090e-02,
            -3.0389264268442007e-02,  8.6684499299869738e-02,
            5.4365532030452843e-02, -1.1850090448995039e-01,
            -2.4574663651253167e-01,  5.0606647353540021e-02,
            -1.1179254494673002e-01,  1.5746625930135386e-01,
            -2.3653025671773734e-01, -2.4326576699636770e-01,
            5.1089622549619594e-02, -2.8901934374460203e-01,
            -2.6451534372339578e-02,  5.4045829899974578e-02,
            7.5844174532653701e-03,  9.5261278786040723e-02,
            6.5117432591824925e-02,  1.5374072905554484e-01,
            6.6944827374030014e-01,  2.5045538719576737e-03,
            -5.5672913354967879e-02,  1.2051210553600417e-02,
            3.3658431259863966e-02, -3.1395677687489406e-03,
            4.7661017511192831e-02, -2.2229335830902394e-01],
          [ 1.9569879043235514e-01, -9.1769753653107577e-02,
            2.7141769527027171e-01,  2.2785564717029946e-01,
            6.4057719170856758e-02, -3.7788206214948872e-03,
            9.7259287514508460e-03,  1.6918261328737952e-01,
            6.8155784376799586e-04, -1.4846652373116371e-02,
            9.7665427524227605e-02,  1.4020779957899679e-01,
            5.4803013440760974e-02, -3.7770889485239614e-02,
            2.0161818269196646e-01,  1.3431772896192445e-01,
            -2.2780324141178667e-02, -1.3299949529057514e-01,
            5.6952253822862586e-01,  1.7551693338628394e-01,
            -3.8851158821630960e-01, -8.2597118671349307e-02,
            -5.5521724833590726e-02,  1.8126259477529724e-01,
            1.7814975368438311e-02, -6.5528218308503153e-02,
            3.7971760553771383e-02, -1.5071623691597721e-01,
            2.1592446351812103e-01, -5.6402536331480002e-04,
            4.5088070248228272e-02,  2.6712876881033590e-02,
            -5.4087768899409383e-03,  6.8686308808012492e-02,
            3.2287080492312645e-02, -2.2653956175799139e-01],
          [ 2.1117037773522207e-01, -5.0164247531242157e-02,
            2.6588099000556803e-01,  9.2461134185888125e-02,
            -1.8638912752062822e-01, -1.3326201088302150e-02,
            -1.5139012219398481e-02,  5.6526342555140038e-02,
            -2.1347405801495557e-02,  4.2134620640229903e-03,
            1.6189227618448768e-01, -4.0274584225345120e-02,
            -5.6430110607539385e-02, -5.8413256975427548e-02,
            5.2327365554425583e-02,  1.0547316593589447e-01,
            -1.0141590903757328e-01,  2.2750086641208328e-03,
            -2.9965053997941909e-01,  1.5580924251156411e-03,
            -9.8801397992561726e-02,  7.0133690173366392e-01,
            2.9288631311505543e-02,  3.2187639373342534e-02,
            8.5847997795661615e-02,  2.0571325754758280e-01,
            7.4079833507648560e-02,  1.5568547966076893e-01,
            -4.9689302197244593e-02,  7.8435365554783448e-02,
            4.8351735020509205e-02, -1.7685071128733182e-01,
            6.5889048949493989e-03,  8.0297089881752479e-02,
            3.9088810533135447e-02, -2.3078576520695873e-01],
          [ 2.2664196503808903e-01, -3.6435359235223168e-02,
            2.7461198824493543e-01,  8.5347376974543573e-02,
            -2.1059797477235808e-02,  1.1448326379020789e-02,
            -2.6592754399652377e-02,  2.5891172442431810e-02,
            2.8366243844641929e-02, -2.0536075588459556e-02,
            6.6444382000443650e-05, -6.6068428617317751e-02,
            2.3676624954254568e-02,  2.2112015932022797e-01,
            3.6011261258148117e-02,  6.3110902119789564e-02,
            -6.5129709470743133e-02, -4.8955274099800709e-02,
            1.5625642089103450e-01,  1.1336968441478927e-01,
            7.1887047535547766e-01, -1.4060033754799098e-01,
            -4.3732646616641863e-02, -2.9113406474813336e-01,
            -5.4252028224128682e-02,  8.5563234976626823e-02,
            -9.8842092892354998e-03, -8.6014269752744857e-02,
            -5.3867992496449059e-02,  1.0226004671603665e-01,
            2.0616418999784455e-01, -6.6321426514466278e-02,
            1.7485733797709232e-02,  1.0373147806260606e-02,
            3.9178042791043720e-02, -2.3503196865592610e-01]]), array([-1.8988227080038084e+03, -8.1652460579197793e-12,
          -6.8293671717855184e-12, -5.0961343548435651e-12,
          -4.6422244875241180e-12, -4.0432649621797409e-12,
          -4.6750947941168519e-13, -4.2866623066103143e-13,
          -3.9638626555876315e-13, -3.4647469398250028e-13,
          -3.2765729675497798e-13, -3.0727463002427591e-13,
          -2.9879803908775378e-13, -2.4080245315867009e-13,
          -2.1775959053373055e-13, -1.8534745675222213e-13,
          -1.5959779217062472e-13, -1.0879546752449559e-13,
          -9.0067575069985811e-14, -5.3973885458936187e-14,
          -4.6064162488080463e-14,  6.1429074771130427e-15,
            1.3659631287864453e-14,  3.4753391317142145e-14,
            8.7547004653142170e-14,  1.2585089324337818e-13,
            1.5745245909745148e-13,  2.0606204849135956e-13,
            2.1792577470203850e-13,  2.6674476798831050e-13,
            3.0421425292401405e-13,  3.1193691330212636e-13,
            3.1270969371399125e-13,  4.3446674157388007e-13,
            1.6764394233642590e-12,  2.5208822708003838e+04])),
    mlir_module_text=r"""
module @jit__lambda_ {
  func.func public @main() -> (tensor<36x36xf64> {jax.result_info = "[0]"}, tensor<36xf64> {jax.result_info = "[1]"}) {
    %0 = stablehlo.iota dim = 0 : tensor<1296xf64>
    %1 = stablehlo.reshape %0 : (tensor<1296xf64>) -> tensor<36x36xf64>
    %2 = stablehlo.transpose %1, dims = [1, 0] : (tensor<36x36xf64>) -> tensor<36x36xf64>
    %3 = stablehlo.add %1, %2 : tensor<36x36xf64>
    %4 = stablehlo.constant dense<2.000000e+00> : tensor<f64>
    %5 = stablehlo.broadcast_in_dim %4, dims = [] : (tensor<f64>) -> tensor<36x36xf64>
    %6 = stablehlo.divide %3, %5 : tensor<36x36xf64>
    %7 = call @tril(%6) : (tensor<36x36xf64>) -> tensor<36x36xf64>
    %8 = stablehlo.custom_call @cusolver_syevd(%7) {api_version = 2 : i32, backend_config = "\01\00\00\00\00\00\00\00\01\00\00\00$\00\00\00Y\98\00\00", operand_layouts = [dense<[0, 1]> : tensor<2xindex>], output_operand_aliases = [#stablehlo.output_operand_alias<output_tuple_indices = [0], operand_index = 0, operand_tuple_indices = []>], result_layouts = [dense<[0, 1]> : tensor<2xindex>, dense<0> : tensor<1xindex>, dense<> : tensor<0xindex>, dense<0> : tensor<1xindex>]} : (tensor<36x36xf64>) -> tuple<tensor<36x36xf64>, tensor<36xf64>, tensor<i32>, tensor<39001xf64>>
    %9 = stablehlo.get_tuple_element %8[0] : (tuple<tensor<36x36xf64>, tensor<36xf64>, tensor<i32>, tensor<39001xf64>>) -> tensor<36x36xf64>
    %10 = stablehlo.get_tuple_element %8[1] : (tuple<tensor<36x36xf64>, tensor<36xf64>, tensor<i32>, tensor<39001xf64>>) -> tensor<36xf64>
    %11 = stablehlo.get_tuple_element %8[2] : (tuple<tensor<36x36xf64>, tensor<36xf64>, tensor<i32>, tensor<39001xf64>>) -> tensor<i32>
    %12 = stablehlo.get_tuple_element %8[3] : (tuple<tensor<36x36xf64>, tensor<36xf64>, tensor<i32>, tensor<39001xf64>>) -> tensor<39001xf64>
    %13 = stablehlo.constant dense<0> : tensor<i32>
    %14 = stablehlo.broadcast_in_dim %13, dims = [] : (tensor<i32>) -> tensor<i32>
    %15 = stablehlo.compare  EQ, %11, %14,  SIGNED : (tensor<i32>, tensor<i32>) -> tensor<i1>
    %16 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1x1xi1>
    %17 = stablehlo.constant dense<0x7FF8000000000000> : tensor<f64>
    %18 = stablehlo.broadcast_in_dim %17, dims = [] : (tensor<f64>) -> tensor<36x36xf64>
    %19 = stablehlo.broadcast_in_dim %16, dims = [0, 1] : (tensor<1x1xi1>) -> tensor<36x36xi1>
    %20 = stablehlo.select %19, %9, %18 : tensor<36x36xi1>, tensor<36x36xf64>
    %21 = stablehlo.broadcast_in_dim %15, dims = [] : (tensor<i1>) -> tensor<1xi1>
    %22 = stablehlo.constant dense<0x7FF8000000000000> : tensor<f64>
    %23 = stablehlo.broadcast_in_dim %22, dims = [] : (tensor<f64>) -> tensor<36xf64>
    %24 = stablehlo.broadcast_in_dim %21, dims = [0] : (tensor<1xi1>) -> tensor<36xi1>
    %25 = stablehlo.select %24, %10, %23 : tensor<36xi1>, tensor<36xf64>
    return %20, %25 : tensor<36x36xf64>, tensor<36xf64>
  }
  func.func private @tril(%arg0: tensor<36x36xf64>) -> tensor<36x36xf64> {
    %0 = stablehlo.iota dim = 0 : tensor<36x36xi32>
    %1 = stablehlo.constant dense<0> : tensor<i32>
    %2 = stablehlo.broadcast_in_dim %1, dims = [] : (tensor<i32>) -> tensor<36x36xi32>
    %3 = stablehlo.add %0, %2 : tensor<36x36xi32>
    %4 = stablehlo.iota dim = 1 : tensor<36x36xi32>
    %5 = stablehlo.compare  GE, %3, %4,  SIGNED : (tensor<36x36xi32>, tensor<36x36xi32>) -> tensor<36x36xi1>
    %6 = stablehlo.constant dense<0.000000e+00> : tensor<f64>
    %7 = stablehlo.broadcast_in_dim %6, dims = [] : (tensor<f64>) -> tensor<36x36xf64>
    %8 = stablehlo.select %5, %arg0, %7 : tensor<36x36xi1>, tensor<36x36xf64>
    return %8 : tensor<36x36xf64>
  }
}
""",
        mlir_module_serialized=b"ML\xefR\x03MLIRxxx-trunk\x00\x01-\x05\x01\x05\x01\x03\x05\x03\x1d\x07\t\x0b\r\x0f\x11\x13\x15\x17\x19\x1b\x1d\x1f!\x03^\x02\xeb5\x01\x95\x0f\x17\x13\x07\x0f\x0b\x0b\x0b\x0b\x0b\x17\x0b\x0b\x0b\x0b\x13\x0b\x13\x0f\x0b\x0b\x17\x0f\x13\x13\x0b33\x0b\x0f\x0b\x0b\x13\x0f\x0b\x1b\x0f\x0b\x13\x0f\x0b\x0f\x0b\x0f\x0b\x0f\x0b\x13\x0b\x0f\x0b\x0f\x0b\x13\x0b\x13\x0bK\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x0b\x13\x13\x13\x13\x1b\x13\x13\x03W\x0b\x0b\x0f\x0b\x0bO/\x0b\x13\x13\x0b\x13\x0b\x0b\x0b\x0b\x0b\x0b\x0f\x1f\x0f\x0f\x0b/O/\x0b\x0b\x0b\x0b\x0f\x0f\x17\x1b\x0f\x0f\x0f\x0f\x0f\x0b/O/\x035\x17\x0f\x07\x0f\x07\x13\x07\x07\x17\x07\x17\x13\x1b\x17\x17\x13\x17\x1b\x13\x13\x13\x0f\x17\x13\x13\x13\x02\xa6\x08\x1d\x85\x03\x17\x11R\x04\x01\x03\x03\x13\xbd\x1f\x1d9\x03\x05#\x05%\x05'\x05)\x05+\x17\x11N\x04\x01\x05-\x05/\x051\x053\x03\x03!\xb9\x055\x03\x03\x0b\xbb\x1d?\x03\x057\x059\x17\x11F\x04\x01\x1dm\x15\x03\x03\x0b\xe5\x03\x03\x0f3\x05;\x03\x0b\x17\x95\x19\xa3\x1b\xa5\x0f\xaf\x1d\xb1\x03\x0b\x17\x99\x19\xb5\x1b\x99\x0f\x9b\x1d\xb7\x05=\x1d=\x03\x05?\x05A\x03\x03!\xbf\x1dE\x03\x05C\x03\x05'\x9d)\xc1\x1dK\x03\x05E\x03\x03\x0b\xc3\x1dQ\x03\x05G\x1dU\x03\x05I\x1dY+\x05K\x1d]+\x05M\x03\x03a\xc5\x05O\x1de\x15\x05Q\x1di\x15\x05S\x03\x03\x0b\xc7\x05U\x03\x03q\x9b\x05W\x03\x11u\xc9w\xcby\xcd{\x95}\xcf\x7f\xd1\x81\xd3\x83\xd7\x05Y\x05[\x05]\x05_\x05a\x05c\x05e\x05g\x05i\x03\x03\r\xdb\x03\x03\r\xdd\x03\x03\r\xdf\x03\x03\r\xe1\x03\x05'\x9d)\xe3\x03\x03\x13\xe7\x03\x03\x13\xe9\x03\x01\x1dk\x03\x03\xb3\x1dm\t\x07\x1f%!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f'\x11\x00\x00\x00\x00\x00\x00\x00\x00#\x1b\x03\x05\xa7\xab\r\x03\x97\xa9\x1do\r\x03\x97\xad\x1dq\x1ds\x1du\r\x01#\x1d\x1dw\x13\r\x01\x1f\x07\t\x00\x00\x00\x00\x1f\x1f\x01\x13\r\x05\x07\x05\x1f\x03\x11\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x17!\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x1f\x03\x11\x00\x00\x00\x00\x00\x00\x00@\x0b\x05\x1dy\x1d{\x05\x01\x03\x03\x9f\x03\x03\xd5\x15\x03\x01\x01\x01\x03\t\x9f\xa1\xd9\xa1\x1f)\x01\x13\x05\x01\x13\x05\x05\x13\x05\t\x13\x05\r\x07\x01\x1f\x03\x11\x00\x00\x00\x00\x00\x00\xf8\x7f\x1f\x17!\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x1f3\x11\x00\x00\x00\x00\x00\x00\x00\x00)\x05\x91\x91\t)\x01\t\x1b)\x01\x05\x0b)\x03\x91\t\x1d\x01)\x05\x91\x91\x05\x13)\x05\x91\x91\x0f)\x03\t\r)\x03\x94\x85\t\t\x11\x01\x05\x01\x0b\x11\x03\x01\x03\x01)\x03\x01\r)\x03\x82(\t/\t\x01\x0b\x07\x19)\x03\t\x13)\x03\x05\x13)\x03\x01\x13)\x01\x0f)\x05\x05\x05\x0f)\x03\x05\x0f)\x03\x91\x0f)\x03\x05\r\x04\xc6\x04\x05\x01\x11\x071\x07\x03\x01\t\r\x11\x075\x05\x035m\t\x03W\x1f\x03!\x15\x06[\x03\x01\x03\x01\x17\x07c_\x03\x01\x03\x03\x0f\x06g\x03\x01\x05\x03\x05\x05\x03\x07k\x03\x03\x03\x07-\x05\x03\x01\x03\t\x19\x06-\x03\x01\x05\x07\x0b\x1b\x07\to\x03\x01\x03\r\x1d\x07\x01s\x03#\x03\x0f\x07\x07\x01\x87\x03\x01\x03\x11\x07\x07\x01\x89\x03\x0b\x03\x11\x07\x07\x01\x8b\x03\x07\x03\x11\x07\x07\x01\x8d\x03\x19\x03\x11\x05\x03\x01#\x03\x07\x03\x07\x01\x05\x03\x07\x03\x1b\x11\x07\x01\x8f\x03+\x05\x17\x1d\x03\x07\x01\x05\x03-\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x01\x03#\x03\x07\x01\x91\x03\x15\x03!\x0b\x06\x01\x03\x01\x07'\x13%\x03\x07\x01\x05\x03/\x03\x1f\x05\x03\x01/\x03\x03\x03\x07\x01\x05\x03\x0b\x03-\x03\x07\x01\x93\x031\x03+\x0b\x06\x01\x03\x0b\x071\x15/\x13\x04\x07\x05)3\r\x11\t7\x05\x03\x15+\x03\x01\x07\t\x03;\x1f\x03\x11\x05\x03\t#\x03\x07\x03\x07%\x05\x03\x11\x03\x05\x0f\x06%\x03\x11\x05\x03\x07\t\x03CA\x03\x11\x11\x07IG\x03\x15\x05\t\x0b\x05\x03\tM\x03\x03\x03\x07O\x05\x03\x01\x03\x0f\x0b\x06S\x03\x01\x07\r\x01\x11\x13\x04\t\x03\x13\x06\x03\x01\x05\x01\x00.\x1a}\x1f+\x11\x0f\x0b\t\t\x0b!\x7f\x1f/!!)#\x1f\x19\x0f99m\x19\x89\x8dW\xb7K\x9fM\x9f\x96\x04\x1b+\x1b\x1f\x1f\x15\x1d\x15+\x83\x13\r\r\x1f\x11\x15\x1b\x17\x15\x17\x0f\x11\x15\x11+\x19)\x0f\x0b\x11builtin\x00vhlo\x00module\x00broadcast_in_dim_v1\x00constant_v1\x00get_tuple_element_v1\x00iota_v1\x00select_v1\x00func_v1\x00add_v1\x00compare_v1\x00return_v1\x00reshape_v1\x00transpose_v1\x00divide_v1\x00call_v1\x00custom_call_v1\x00value\x00index\x00sym_name\x00third_party/py/jax/experimental/jax2tf/tests/back_compat_test.py\x00broadcast_dimensions\x00arg_attrs\x00function_type\x00res_attrs\x00sym_visibility\x00iota_dimension\x00compare_type\x00comparison_direction\x00jit__lambda_\x00jit(<lambda>)/jit(main)/pjit[in_shardings=(UnspecifiedValue,) out_shardings=(UnspecifiedValue,) resource_env=None donated_invars=(False,) name=tril in_positional_semantics=(<_PositionalSemantics.GLOBAL: 1>,) out_positional_semantics=_PositionalSemantics.GLOBAL keep_unused=False inline=False]\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(36, 36) dimension=0]\x00jit(<lambda>)/jit(main)/jit(tril)/add\x00jit(<lambda>)/jit(main)/jit(tril)/iota[dtype=int32 shape=(36, 36) dimension=1]\x00jit(<lambda>)/jit(main)/jit(tril)/ge\x00jit(<lambda>)/jit(main)/jit(tril)/broadcast_in_dim[shape=(36, 36) broadcast_dimensions=()]\x00jit(<lambda>)/jit(main)/jit(tril)/select_n\x00jit(<lambda>)/jit(main)/iota[dtype=float64 shape=(1296,) dimension=0]\x00jit(<lambda>)/jit(main)/reshape[new_sizes=(36, 36) dimensions=None]\x00permutation\x00jit(<lambda>)/jit(main)/transpose[permutation=(1, 0)]\x00jit(<lambda>)/jit(main)/add\x00jit(<lambda>)/jit(main)/div\x00callee\x00api_version\x00backend_config\x00call_target_name\x00called_computations\x00has_side_effect\x00operand_layouts\x00output_operand_aliases\x00result_layouts\x00jit(<lambda>)/jit(main)/eigh[lower=True sort_eigenvalues=True]\x00jax.result_info\x00tril\x00[0]\x00[1]\x00main\x00public\x00private\x00\x01\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00$\x00\x00\x00Y\x98\x00\x00\x00cusolver_syevd\x00",
        xla_call_module_version=4,
    )  # End paste
)
