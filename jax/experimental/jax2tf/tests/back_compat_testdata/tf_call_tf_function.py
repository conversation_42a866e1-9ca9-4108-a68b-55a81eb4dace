# Copyright 2023 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import datetime
from numpy import array, float32


# Pasted from the test output (see back_compat_test.py module docstring)
data_2023_07_29 = dict(
    testdata_version=1,
    platform='cpu',
    custom_call_targets=['tf.call_tf_function'],
    serialized_date=datetime.date(2023, 7, 29),
    inputs=(array([0.5, 0.7], dtype=float32),),
    expected_outputs=(array([0.88726   , 0.79956985], dtype=float32),),
    mlir_module_text=r"""
# First the MLIR module:
#loc = loc(unknown)
module @jit_func attributes {jax.uses_shape_polymorphism = false, mhlo.num_partitions = 1 : i32, mhlo.num_replicas = 1 : i32} {
  func.func public @main(%arg0: tensor<2xf32> {mhlo.sharding = "{replicated}"} loc(unknown)) -> (tensor<2xf32> {jax.result_info = ""}) {
    %0 = stablehlo.custom_call @tf.call_tf_function(%arg0) {api_version = 2 : i32, has_side_effect = true, tf.backend_config = {called_index = 0 : i64, has_token_input_output = false}} : (tensor<2xf32>) -> tensor<2xf32> loc(#loc3)
    %1 = stablehlo.cosine %0 : tensor<2xf32> loc(#loc4)
    return %1 : tensor<2xf32> loc(#loc)
  } loc(#loc)
} loc(#loc)
#loc1 = loc("third_party/py/jax/experimental/jax2tf/tests/back_compat_tf_test.py":144:0)
#loc2 = loc("third_party/py/jax/experimental/jax2tf/tests/back_compat_tf_test.py":143:0)
#loc3 = loc("jit(func)/jit(main)/call_tf[callable_flat_tf=<function call_tf.<locals>.make_call.<locals>.callable_flat_tf at 0x7fb0c9a68430> function_flat_tf=<googlex.third_party.tensorflow.python.eager.polymorphic_function.polymorphic_function.Function object at 0x7fb0c9a57d60> args_flat_sig_tf=(TensorSpec(shape=(2,), dtype=tf.float32, name=None),) output_avals=(ShapedArray(float32[2]),) has_side_effects=True ordered=False call_tf_graph=True]"(#loc1))
#loc4 = loc("jit(func)/jit(main)/cos"(#loc2))

# Then the tf.Graph:
node {
  name: "the_input"
  op: "Placeholder"
  attr {
    key: "_user_specified_name"
    value {
      s: "the_input"
    }
  }
  attr {
    key: "dtype"
    value {
      type: DT_FLOAT
    }
  }
  attr {
    key: "shape"
    value {
      shape {
        dim {
          size: 2
        }
      }
    }
  }
}
node {
  name: "jax2tf_arg_0"
  op: "Identity"
  input: "the_input"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
}
node {
  name: "XlaSharding"
  op: "XlaSharding"
  input: "jax2tf_arg_0"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
  attr {
    key: "_XlaSharding"
    value {
      s: ""
    }
  }
  attr {
    key: "sharding"
    value {
      s: ""
    }
  }
  attr {
    key: "unspecified_dims"
    value {
      list {
      }
    }
  }
}
node {
  name: "XlaCallModule"
  op: "XlaCallModule"
  input: "XlaSharding"
  attr {
    key: "Sout"
    value {
      list {
        shape {
          dim {
            size: 2
          }
        }
      }
    }
  }
  attr {
    key: "Tin"
    value {
      list {
        type: DT_FLOAT
      }
    }
  }
  attr {
    key: "Tout"
    value {
      list {
        type: DT_FLOAT
      }
    }
  }
  attr {
    key: "dim_args_spec"
    value {
      list {
      }
    }
  }
  attr {
    key: "disabled_checks"
    value {
      list {
      }
    }
  }
  attr {
    key: "function_list"
    value {
      list {
        func {
          name: "__inference_callable_flat_tf_9"
        }
      }
    }
  }
  attr {
    key: "has_token_input_output"
    value {
      b: false
    }
  }
  attr {
    key: "module"
    value {
      s: "ML\357R\001StableHLO_v0.9.0\000\001\031\005\001\003\001\003\005\003\t\007\t\013\r\003\207g\r\001?\007\017\013\013+\013\017\013\013\0133\013\013\013\013S\013\013\013\013\013\013\013\013\013\017\013\027\017\013\027\003)\013\013\017\023\013\013\013\017\023\013\013\013\013\013\013\033\013\017\013\013\001\005\013\017\003\t\023\027\007\007\002\272\002\037\021\003\005\005\017\005\021\003\t\013\r\017\003\021\003\005\023\005\023\021\001\000\005\025\005\027\005\031\003\013\027C\031K\033M\005S\035U\005\033\005\035\005\037\005!\003\023!W#A%Y\'?)[+?-?/?1]\005#\005%\005\'\005)\005+\005-\005/\0051\0053\03557\0055\027\007B\002\001\035;=\0057\027\007>\002\001\003\001\0359\003\003E\r\003GI\035;\035=#\007\003\003O\r\003QA\035?\035A\035C\013\005\035E\005\003\r\005_ace\035G\023\013\001\035I\005\001\001\t\001\002\002)\003\t\t\021\003\005\003\005\t\035\004Q\005\001\021\001\t\007\003\001\005\003\021\001\025\005\003\007\017\003\005\001\005\0073\037\003\005\003\001\007\0069\003\005\003\003\t\004\001\003\005\006\003\001\005\001\000\006\020K/\033)\017\013!\033\035\0031\312\006%\037/!!)#\037\031\037\025\035\025\023%)9\211\023\025\025\037\021\017\013\021builtin\000vhlo\000module\000func_v1\000custom_call_v1\000cosine_v1\000return_v1\000sym_name\000third_party/py/jax/experimental/jax2tf/tests/back_compat_tf_test.py\000jax.uses_shape_polymorphism\000mhlo.num_partitions\000mhlo.num_replicas\000jit_func\000arg_attrs\000function_type\000res_attrs\000sym_visibility\000api_version\000backend_config\000call_target_name\000called_computations\000has_side_effect\000operand_layouts\000output_operand_aliases\000result_layouts\000tf.backend_config\000jit(func)/jit(main)/call_tf[callable_flat_tf=<function call_tf.<locals>.make_call.<locals>.callable_flat_tf at 0x7fb0c9a68430> function_flat_tf=<googlex.third_party.tensorflow.python.eager.polymorphic_function.polymorphic_function.Function object at 0x7fb0c9a57d60> args_flat_sig_tf=(TensorSpec(shape=(2,), dtype=tf.float32, name=None),) output_avals=(ShapedArray(float32[2]),) has_side_effects=True ordered=False call_tf_graph=True]\000jit(func)/jit(main)/cos\000\000mhlo.sharding\000{replicated}\000jax.result_info\000main\000public\000tf.call_tf_function\000called_index\000has_token_input_output\000"
    }
  }
  attr {
    key: "platforms"
    value {
      list {
        s: "CPU"
      }
    }
  }
  attr {
    key: "version"
    value {
      i: 6
    }
  }
}
node {
  name: "Identity"
  op: "Identity"
  input: "XlaCallModule"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
}
node {
  name: "IdentityN"
  op: "IdentityN"
  input: "XlaCallModule"
  input: "jax2tf_arg_0"
  attr {
    key: "T"
    value {
      list {
        type: DT_FLOAT
        type: DT_FLOAT
      }
    }
  }
  attr {
    key: "_gradient_op_type"
    value {
      s: "CustomGradient-10"
    }
  }
}
node {
  name: "jax2tf_out"
  op: "Identity"
  input: "IdentityN"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
}
node {
  name: "the_result"
  op: "Identity"
  input: "jax2tf_out"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
}
node {
  name: "Identity_1"
  op: "Identity"
  input: "the_result"
  input: "^NoOp"
  attr {
    key: "T"
    value {
      type: DT_FLOAT
    }
  }
}
node {
  name: "NoOp"
  op: "NoOp"
  input: "^XlaCallModule"
}
library {
  function {
    signature {
      name: "__inference_callable_flat_tf_9"
      input_arg {
        name: "args_tf_flat_0"
        type: DT_FLOAT
      }
      output_arg {
        name: "identity"
        type: DT_FLOAT
      }
    }
    node_def {
      name: "Sin"
      op: "Sin"
      input: "args_tf_flat_0"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
    }
    node_def {
      name: "EnsureShape"
      op: "EnsureShape"
      input: "Sin:y:0"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
      attr {
        key: "shape"
        value {
          shape {
            dim {
              size: 2
            }
          }
        }
      }
    }
    node_def {
      name: "Identity"
      op: "Identity"
      input: "EnsureShape:output:0"
      attr {
        key: "T"
        value {
          type: DT_FLOAT
        }
      }
    }
    ret {
      key: "identity"
      value: "Identity:output:0"
    }
    attr {
      key: "_XlaMustCompile"
      value {
        b: false
      }
    }
    attr {
      key: "_construction_context"
      value {
        s: "kEagerRuntime"
      }
    }
    arg_attr {
      key: 0
      value {
        attr {
          key: "_output_shapes"
          value {
            list {
              shape {
                dim {
                  size: 2
                }
              }
            }
          }
        }
        attr {
          key: "_user_specified_name"
          value {
            s: "args_tf_flat_0"
          }
        }
      }
    }
  }
}
versions {
  producer: 1570
  min_consumer: 12
}
""",
    mlir_module_serialized=b'Li8uL0BQYXhIZWFkZXIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwADAwMDAwMDAwMDM0ADAwMDAwMDAwMDAwADAxMDIxMgAgeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1c3RhcgAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyOCBtdGltZT0xNjkwNjQzMzExLjQ4ODIwNjQKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHNhdmVkX21vZGVsLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwNzU1ADAxMTYyNzIAMDAxMTYxMAAwMDAwMDAwMDAwMAAxNDQ2MTIyNTU1NwAwMTIyNjcAIDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDBmb3JnZS0wMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGVuZwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALi8uL0BQYXhIZWFkZXIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwADAwMDAwMDAwMDM0ADAwMDAwMDAwMDAwADAxMDIxMgAgeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1c3RhcgAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyOCBtdGltZT0xNjkwNjQzMzExLjQ4NjIwNjMKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHNhdmVkX21vZGVsL2Fzc2V0cy8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwNzU1ADAxMTYyNzIAMDAxMTYxMAAwMDAwMDAwMDAwMAAxNDQ2MTIyNTU1NwAwMTM1NzEAIDUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDBmb3JnZS0wMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGVuZwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALi8uL0BQYXhIZWFkZXIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwADAwMDAwMDAwMDM0ADAwMDAwMDAwMDAwADAxMDIxMgAgeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1c3RhcgAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyOCBtdGltZT0xNjkwNjQzMzExLjQ4ODIwNjQKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHNhdmVkX21vZGVsL2ZpbmdlcnByaW50LnBiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwNjQ0ADAxMTYyNzIAMDAxMTYxMAAwMDAwMDAwMDA3MAAxNDQ2MTIyNTU1NwAwMTUxMzYAIDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDBmb3JnZS0wMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGVuZwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMgIIASjqjeiel7aX4lUghNC3mLHo9IU1GOHY4srBh5PxgwEQzNThjcaTk8R7CLrl3Ij9ldCz8QEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAuLy4vQFBheEhlYWRlcgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAwMDAwMAAwMDAwMDAwADAwMDAwMDAAMDAwMDAwMDAwMzQAMDAwMDAwMDAwMDAAMDEwMjEyACB4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHVzdGFyADAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADI4IG10aW1lPTE2OTA2NDMzMTEuNDg2MjA2MwoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAc2F2ZWRfbW9kZWwvc2F2ZWRfbW9kZWwucGIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMDA2NDQAMDExNjI3MgAwMDExNjEwADAwMDAwMDI1NDcxADE0NDYxMjI1NTU3ADAxNTEwNQAgMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1c3RhcgAwMGZvcmdlLTAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZW5nAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIARK0VjrgBQo4CggSBGNhbGwIAQoOEgpzaWduYXR1cmVzCAIiHAoUX2dlbmVyaWNfdXNlcl9vYmplY3QSBAgBEAEKxgFCwwEKFl9faW5mZXJlbmNlX3RmX2Z1bmNfMTkSCXRoZV9pbnB1dBgBIpsBKgIKAAqUAbIDkAESGBIQmgMNCgtqCXRoZV9pbnB1dAoEYXJncxINEgIKAAoHdmFyYXJncxILEgIKAAoFdmFya3cSDhICCgAKCGRlZmF1bHRzEhESA5oDAAoKa3dvbmx5YXJncxIUEgIKAAoOa3dvbmx5ZGVmYXVsdHMSEhIDqgMACgthbm5vdGF0aW9ucwoLRnVsbEFyZ1NwZWMKLAoTEg9zZXJ2aW5nX2RlZmF1bHQIAyIVCg1zaWduYXR1cmVfbWFwEgQIARABCtABQs0BCiBfX2luZmVyZW5jZV9zaWduYXR1cmVfd3JhcHBlcl8yNxIJdGhlX2lucHV0GAEimwEqAgoACpQBsgOQARILEgOaAwAKBGFyZ3MSDRICCgAKB3ZhcmFyZ3MSCxICCgAKBXZhcmt3Eg4SAgoACghkZWZhdWx0cxIeEhCaAw0KC2oJdGhlX2lucHV0Cgprd29ubHlhcmdzEhQSAgoACg5rd29ubHlkZWZhdWx0cxISEgOqAwAKC2Fubm90YXRpb25zCgtGdWxsQXJnU3BlYxKAAQogX19pbmZlcmVuY2Vfc2lnbmF0dXJlX3dyYXBwZXJfMjcSXBoyogMvCgOiAwAKKKoDJQojCgl0aGVfaW5wdXQSFooCEwoJdGhlX2lucHV0EgQSAggCGAEiJqoDIwohCghvdXRwdXRfMBIVigISCghvdXRwdXRfMBIEEgIIAhgBElcKFl9faW5mZXJlbmNlX3RmX2Z1bmNfMTkSPRologMiChuiAxgKFooCEwoJdGhlX2lucHV0EgQSAggCGAEKA6oDACIUigIRCgd1bmtub3duEgQSAggCGAEqPgoVX19zYXZlZF9tb2RlbF9pbml0X29wEiUSIwoVX19zYXZlZF9tb2RlbF9pbml0X29wEgoKBE5vT3AaAhgBKpUBCg9zZXJ2aW5nX2RlZmF1bHQSgQEKMgoJdGhlX2lucHV0EiUKG3NlcnZpbmdfZGVmYXVsdF90aGVfaW5wdXQ6MBABGgQSAggCEi8KCG91dHB1dF8wEiMKGVN0YXRlZnVsUGFydGl0aW9uZWRDYWxsOjAQARoEEgIIAhoadGVuc29yZmxvdy9zZXJ2aW5nL3ByZWRpY3QiHwoTc2F2ZWRfbW9kZWxfbWFpbl9vcBIICgYKBE5vT3AaTAoQc2F2ZXJfZmlsZW5hbWU6MBIbU3RhdGVmdWxQYXJ0aXRpb25lZENhbGxfMTowGhlTdGF0ZWZ1bFBhcnRpdGlvbmVkQ2FsbF8yOAISr0EKYgoZc2VydmluZ19kZWZhdWx0X3RoZV9pbnB1dBILUGxhY2Vob2xkZXIqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCKgsKBWR0eXBlEgIwASoPCgVzaGFwZRIGOgQSAggCCqECChdTdGF0ZWZ1bFBhcnRpdGlvbmVkQ2FsbBIXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwaGXNlcnZpbmdfZGVmYXVsdF90aGVfaW5wdXQqDAoDVGluEgUKAzIBASoNCgRUb3V0EgUKAzIBASodChdfY29sbGVjdGl2ZV9tYW5hZ2VyX2lkcxICCgAqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCKiAKGl9yZWFkX29ubHlfcmVzb3VyY2VfaW5wdXRzEgIKACotCgxjb25maWdfcHJvdG8SHRIbggEAOAEyAkoACgcKA0NQVRABCgcKA0dQVRAAKikKAWYSJFIiCiBfX2luZmVyZW5jZV9zaWduYXR1cmVfd3JhcHBlcl8yNwoMCgROb09wEgROb09wCpQBCgVDb25zdBIFQ29uc3QiDS9kZXZpY2U6Q1BVOjAqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqCwoFZHR5cGUSAjAHKlAKBXZhbHVlEkdCRQgHEgBCPwocCggSBGNhbGwIAQoOEgpzaWduYXR1cmVzCAIqAAoCKgAKFwoTEg9zZXJ2aW5nX2RlZmF1bHQIAyoACgIqAApPCg5zYXZlcl9maWxlbmFtZRILUGxhY2Vob2xkZXIqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqCwoFZHR5cGUSAjAHKgsKBXNoYXBlEgI6AAqXAgoZU3RhdGVmdWxQYXJ0aXRpb25lZENhbGxfMRIXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwaDnNhdmVyX2ZpbGVuYW1lGgVDb25zdCoNCgNUaW4SBgoEMgIHByoNCgRUb3V0EgUKAzIBByodChdfY29sbGVjdGl2ZV9tYW5hZ2VyX2lkcxICCgAqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqIAoaX3JlYWRfb25seV9yZXNvdXJjZV9pbnB1dHMSAgoAKi0KDGNvbmZpZ19wcm90bxIdEhuCAQA4ATICSgAKBwoDQ1BVEAEKBwoDR1BVEAAqJAoBZhIfUh0KG19faW5mZXJlbmNlX190cmFjZWRfc2F2ZV80OQqSAgoZU3RhdGVmdWxQYXJ0aXRpb25lZENhbGxfMhIXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwaDnNhdmVyX2ZpbGVuYW1lKgwKA1RpbhIFCgMyAQcqDQoEVG91dBIFCgMyAQcqHQoXX2NvbGxlY3RpdmVfbWFuYWdlcl9pZHMSAgoAKhYKDl9vdXRwdXRfc2hhcGVzEgQKAjoAKiAKGl9yZWFkX29ubHlfcmVzb3VyY2VfaW5wdXRzEgIKACotCgxjb25maWdfcHJvdG8SHRIbggEAOAEyAkoACgcKA0NQVRABCgcKA0dQVRAAKicKAWYSIlIgCh5fX2luZmVyZW5jZV9fdHJhY2VkX3Jlc3RvcmVfNTgS+DcKgAY6RQgAEkEKGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCCiMKFF91c2VyX3NwZWNpZmllZF9uYW1lEgsSCXRoZV9pbnB1dDIyChdTdGF0ZWZ1bFBhcnRpdGlvbmVkQ2FsbBIXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwqKAoVX2NvbnN0cnVjdGlvbl9jb250ZXh0Eg8SDWtFYWdlclJ1bnRpbWUqGQoNX2lucHV0X3NoYXBlcxIICgY6BBICCAIiHQoIaWRlbnRpdHkSEUlkZW50aXR5Om91dHB1dDowGp4CChdTdGF0ZWZ1bFBhcnRpdGlvbmVkQ2FsbBIXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwaCXRoZV9pbnB1dCoMCgNUaW4SBQoDMgEBKg0KBFRvdXQSBQoDMgEBKhUKD19YbGFNdXN0Q29tcGlsZRICKAEqHQoXX2NvbGxlY3RpdmVfbWFuYWdlcl9pZHMSAgoAKhoKDl9vdXRwdXRfc2hhcGVzEggKBjoEEgIIAiogChpfcmVhZF9vbmx5X3Jlc291cmNlX2lucHV0cxICCgAqLQoMY29uZmlnX3Byb3RvEh0SG4IBADgBMgJKAAoHCgNDUFUQAQoHCgNHUFUQACofCgFmEhpSGAoWX19pbmZlcmVuY2VfdGZfZnVuY18xORpiCghJZGVudGl0eRIISWRlbnRpdHkaIFN0YXRlZnVsUGFydGl0aW9uZWRDYWxsOm91dHB1dDowGgVeTm9PcCoHCgFUEgIwASoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIaPAoETm9PcBIETm9PcBoYXlN0YXRlZnVsUGFydGl0aW9uZWRDYWxsKhQKDl9vdXRwdXRfc2hhcGVzEgIKAApcCiBfX2luZmVyZW5jZV9zaWduYXR1cmVfd3JhcHBlcl8yNxINCgl0aGVfaW5wdXQYARoMCghpZGVudGl0eRgBiAEBogEXU3RhdGVmdWxQYXJ0aXRpb25lZENhbGwK+AM6SggAEkYKGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCCigKFF91c2VyX3NwZWNpZmllZF9uYW1lEhASDmFyZ3NfdGZfZmxhdF8wKhUKD19YbGFNdXN0Q29tcGlsZRICKAAqKAoVX2NvbnN0cnVjdGlvbl9jb250ZXh0Eg8SDWtFYWdlclJ1bnRpbWUqGQoNX2lucHV0X3NoYXBlcxIICgY6BBICCAIiHQoIaWRlbnRpdHkSEUlkZW50aXR5Om91dHB1dDowGj8KA1NpbhIDU2luGg5hcmdzX3RmX2ZsYXRfMCoHCgFUEgIwASoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIaWQoLRW5zdXJlU2hhcGUSC0Vuc3VyZVNoYXBlGgdTaW46eTowKgcKAVQSAjABKhoKDl9vdXRwdXRfc2hhcGVzEggKBjoEEgIIAioPCgVzaGFwZRIGOgQSAggCGk8KCElkZW50aXR5EghJZGVudGl0eRoURW5zdXJlU2hhcGU6b3V0cHV0OjAqBwoBVBICMAEqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCCkIKHl9faW5mZXJlbmNlX2NhbGxhYmxlX2ZsYXRfdGZfORISCg5hcmdzX3RmX2ZsYXRfMBgBGgwKCGlkZW50aXR5GAEKlwc6QwgAEj8KFgoOX291dHB1dF9zaGFwZXMSBAoCOgAKJQoUX3VzZXJfc3BlY2lmaWVkX25hbWUSDRILZmlsZV9wcmVmaXgqKAoVX2NvbnN0cnVjdGlvbl9jb250ZXh0Eg8SDWtFYWdlclJ1bnRpbWUqFQoNX2lucHV0X3NoYXBlcxIECgI6ACIhCgppZGVudGl0eV8xEhNJZGVudGl0eV8xOm91dHB1dDowGooBChZSZXN0b3JlVjIvdGVuc29yX25hbWVzEgVDb25zdCINL2RldmljZTpDUFU6MCoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAEqCwoFZHR5cGUSAjAHKjEKBXZhbHVlEihCJggHEgQSAggBQhxfQ0hFQ0tQT0lOVEFCTEVfT0JKRUNUX0dSQVBIGnIKGlJlc3RvcmVWMi9zaGFwZV9hbmRfc2xpY2VzEgVDb25zdCINL2RldmljZTpDUFU6MCoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAEqCwoFZHR5cGUSAjAHKhUKBXZhbHVlEgxCCggHEgQSAggBQgAaowEKCVJlc3RvcmVWMhIJUmVzdG9yZVYyGgtmaWxlX3ByZWZpeBofUmVzdG9yZVYyL3RlbnNvcl9uYW1lczpvdXRwdXQ6MBojUmVzdG9yZVYyL3NoYXBlX2FuZF9zbGljZXM6b3V0cHV0OjAiDS9kZXZpY2U6Q1BVOjAqGAoOX291dHB1dF9zaGFwZXMSBgoEOgIYASoPCgZkdHlwZXMSBQoDMgEHGlkKBE5vT3ASBE5vT3AiDS9kZXZpY2U6Q1BVOjAqJgogX2hhc19tYW51YWxfY29udHJvbF9kZXBlbmRlbmNpZXMSAigBKhQKDl9vdXRwdXRfc2hhcGVzEgIKABpYCghJZGVudGl0eRIISWRlbnRpdHkaC2ZpbGVfcHJlZml4GgVeTm9PcCINL2RldmljZTpDUFU6MCoHCgFUEgIwByoWCg5fb3V0cHV0X3NoYXBlcxIECgI6ABpKCgpJZGVudGl0eV8xEghJZGVudGl0eRoRSWRlbnRpdHk6b3V0cHV0OjAqBwoBVBICMAcqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAKRAoeX19pbmZlcmVuY2VfX3RyYWNlZF9yZXN0b3JlXzU4Eg8KC2ZpbGVfcHJlZml4GAcaDgoKaWRlbnRpdHlfMRgHiAEBCoESOkMIABI/ChYKDl9vdXRwdXRfc2hhcGVzEgQKAjoACiUKFF91c2VyX3NwZWNpZmllZF9uYW1lEg0SC2ZpbGVfcHJlZml4Oj0IARI5ChYKDl9vdXRwdXRfc2hhcGVzEgQKAjoACh8KFF91c2VyX3NwZWNpZmllZF9uYW1lEgcSBUNvbnN0MigKEk1lcmdlVjJDaGVja3BvaW50cxISTWVyZ2VWMkNoZWNrcG9pbnRzKigKFV9jb25zdHJ1Y3Rpb25fY29udGV4dBIPEg1rRWFnZXJSdW50aW1lKhcKDV9pbnB1dF9zaGFwZXMSBgoEOgA6ACIhCgppZGVudGl0eV8xEhNJZGVudGl0eV8xOm91dHB1dDowGncKFFN0YXRpY1JlZ2V4RnVsbE1hdGNoEhRTdGF0aWNSZWdleEZ1bGxNYXRjaBoLZmlsZV9wcmVmaXgiDS9kZXZpY2U6Q1BVOioqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqFQoHcGF0dGVybhIKEgheczM6Ly8uKhpaCgVDb25zdBIFQ29uc3QiDS9kZXZpY2U6Q1BVOioqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqCwoFZHR5cGUSAjAHKhYKBXZhbHVlEg1CCwgHEgBCBS5wYXJ0GmEKB0NvbnN0XzESBUNvbnN0Ig0vZGV2aWNlOkNQVToqKhYKDl9vdXRwdXRfc2hhcGVzEgQKAjoAKgsKBWR0eXBlEgIwByobCgV2YWx1ZRISQhAIBxIAQgpfdGVtcC9wYXJ0GoEBCgZTZWxlY3QSBlNlbGVjdBodU3RhdGljUmVnZXhGdWxsTWF0Y2g6b3V0cHV0OjAaDkNvbnN0Om91dHB1dDowGhBDb25zdF8xOm91dHB1dDowIg0vZGV2aWNlOkNQVToqKgcKAVQSAjAHKhYKDl9vdXRwdXRfc2hhcGVzEgQKAjoAGmYKClN0cmluZ0pvaW4SClN0cmluZ0pvaW4aC2ZpbGVfcHJlZml4Gg9TZWxlY3Q6b3V0cHV0OjAiDS9kZXZpY2U6Q1BVOioqBwoBThICGAIqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAaTAoKbnVtX3NoYXJkcxIFQ29uc3QqFgoOX291dHB1dF9zaGFwZXMSBAoCOgAqCwoFZHR5cGUSAjADKhIKBXZhbHVlEglCBwgDEgA6AQEaZgoVU2hhcmRlZEZpbGVuYW1lL3NoYXJkEgVDb25zdCINL2RldmljZTpDUFU6MCoWCg5fb3V0cHV0X3NoYXBlcxIECgI6ACoLCgVkdHlwZRICMAMqEgoFdmFsdWUSCUIHCAMSADoBABqTAQoPU2hhcmRlZEZpbGVuYW1lEg9TaGFyZGVkRmlsZW5hbWUaE1N0cmluZ0pvaW46b3V0cHV0OjAaHlNoYXJkZWRGaWxlbmFtZS9zaGFyZDpvdXRwdXQ6MBoTbnVtX3NoYXJkczpvdXRwdXQ6MCINL2RldmljZTpDUFU6MCoWCg5fb3V0cHV0X3NoYXBlcxIECgI6ABqHAQoTU2F2ZVYyL3RlbnNvcl9uYW1lcxIFQ29uc3QiDS9kZXZpY2U6Q1BVOjAqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggBKgsKBWR0eXBlEgIwByoxCgV2YWx1ZRIoQiYIBxIEEgIIAUIcX0NIRUNLUE9JTlRBQkxFX09CSkVDVF9HUkFQSBpvChdTYXZlVjIvc2hhcGVfYW5kX3NsaWNlcxIFQ29uc3QiDS9kZXZpY2U6Q1BVOjAqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggBKgsKBWR0eXBlEgIwByoVCgV2YWx1ZRIMQgoIBxIEEgIIAUIAGtgBCgZTYXZlVjISBlNhdmVWMhoaU2hhcmRlZEZpbGVuYW1lOmZpbGVuYW1lOjAaHFNhdmVWMi90ZW5zb3JfbmFtZXM6b3V0cHV0OjAaIFNhdmVWMi9zaGFwZV9hbmRfc2xpY2VzOm91dHB1dDowGgxzYXZldjJfY29uc3QiDS9kZXZpY2U6Q1BVOjAqJgogX2hhc19tYW51YWxfY29udHJvbF9kZXBlbmRlbmNpZXMSAigBKhQKDl9vdXRwdXRfc2hhcGVzEgIKACoPCgZkdHlwZXMSBQoDMgEHGpABCiZNZXJnZVYyQ2hlY2twb2ludHMvY2hlY2twb2ludF9wcmVmaXhlcxIEUGFjaxoaU2hhcmRlZEZpbGVuYW1lOmZpbGVuYW1lOjAaB15TYXZlVjIiDS9kZXZpY2U6Q1BVOjAqBwoBThICGAEqBwoBVBICMAcqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggBGrMBChJNZXJnZVYyQ2hlY2twb2ludHMSEk1lcmdlVjJDaGVja3BvaW50cxovTWVyZ2VWMkNoZWNrcG9pbnRzL2NoZWNrcG9pbnRfcHJlZml4ZXM6b3V0cHV0OjAaC2ZpbGVfcHJlZml4Ig0vZGV2aWNlOkNQVTowKiYKIF9oYXNfbWFudWFsX2NvbnRyb2xfZGVwZW5kZW5jaWVzEgIoASoUCg5fb3V0cHV0X3NoYXBlcxICCgAaZgoISWRlbnRpdHkSCElkZW50aXR5GgtmaWxlX3ByZWZpeBoTXk1lcmdlVjJDaGVja3BvaW50cyINL2RldmljZTpDUFU6MCoHCgFUEgIwByoWCg5fb3V0cHV0X3NoYXBlcxIECgI6ABpRCgpJZGVudGl0eV8xEghJZGVudGl0eRoRSWRlbnRpdHk6b3V0cHV0OjAaBV5Ob09wKgcKAVQSAjAHKhYKDl9vdXRwdXRfc2hhcGVzEgQKAjoAGjcKBE5vT3ASBE5vT3AaE15NZXJnZVYyQ2hlY2twb2ludHMqFAoOX291dHB1dF9zaGFwZXMSAgoACmgKG19faW5mZXJlbmNlX190cmFjZWRfc2F2ZV80ORIPCgtmaWxlX3ByZWZpeBgHEhAKDHNhdmV2Ml9jb25zdBgHGg4KCmlkZW50aXR5XzEYB4gBAaIBEk1lcmdlVjJDaGVja3BvaW50cwrZFDpFCAASQQoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIKIwoUX3VzZXJfc3BlY2lmaWVkX25hbWUSCxIJdGhlX2lucHV0Mh4KDVhsYUNhbGxNb2R1bGUSDVhsYUNhbGxNb2R1bGUqFQoPX1hsYU11c3RDb21waWxlEgIoASooChVfY29uc3RydWN0aW9uX2NvbnRleHQSDxINa0VhZ2VyUnVudGltZSoZCg1faW5wdXRfc2hhcGVzEggKBjoEEgIIAioPCglfbm9pbmxpbmUSAigBIiEKCmlkZW50aXR5XzESE0lkZW50aXR5XzE6b3V0cHV0OjAaSAoMamF4MnRmX2FyZ18wEghJZGVudGl0eRoJdGhlX2lucHV0KgcKAVQSAjABKhoKDl9vdXRwdXRfc2hhcGVzEggKBjoEEgIIAhpqCgtYbGFTaGFyZGluZxILWGxhU2hhcmRpbmcaFWpheDJ0Zl9hcmdfMDpvdXRwdXQ6MCoHCgFUEgIwASoSCgxfWGxhU2hhcmRpbmcSAhIAKhoKDl9vdXRwdXRfc2hhcGVzEggKBjoEEgIIAhq5DAoNWGxhQ2FsbE1vZHVsZRINWGxhQ2FsbE1vZHVsZRoUWGxhU2hhcmRpbmc6b3V0cHV0OjAqEAoEU291dBIICgY6BBICCAIqDAoDVGluEgUKAzIBASoNCgRUb3V0EgUKAzIBASoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIqNQoNZnVuY3Rpb25fbGlzdBIkCiJKIAoeX19pbmZlcmVuY2VfY2FsbGFibGVfZmxhdF90Zl85KtsKCgZtb2R1bGUS0AoSzQpNTO9SAVN0YWJsZUhMT192MC45LjAAARkFAQMBAwUDCQcJCw0Dh2cNAT8HDwsLKwsPCwsLMwsLCwtTCwsLCwsLCwsLDwsXDwsXAykLCw8TCwsLDxMLCwsLCwsbCw8LCwEFCw8DCRMXBwcCugIfEQMFBQ8FEQMJCw0PAxEDBRMFExEBAAUVBRcFGQMLF0MZSxtNBVMdVQUbBR0FHwUhAxMhVyNBJVknPylbKz8tPy8/MV0FIwUlBScFKQUrBS0FLwUxBTMdNTcFNRcHQgIBHTs9BTcXBz4CAQMBHTkDA0UNA0dJHTsdPSMHAwNPDQNRQR0/HUEdQwsFHUUFAw0FX2FjZR1HEwsBHUkFAQEJAQICKQMJCREDBQMFCR0EUQUBEQEJBwMBBQMRARUFAwcPAwUBBQczHwMFAwEHBjkDBQMDCQQBAwUGAwEFAQAGEEsvGykPCyEbHQMxygYlHy8hISkjHxkfFR0VEyUpOYkTFRUfEQ8LEWJ1aWx0aW4AdmhsbwBtb2R1bGUAZnVuY192MQBjdXN0b21fY2FsbF92MQBjb3NpbmVfdjEAcmV0dXJuX3YxAHN5bV9uYW1lAHRoaXJkX3BhcnR5L3B5L2pheC9leHBlcmltZW50YWwvamF4MnRmL3Rlc3RzL2JhY2tfY29tcGF0X3RmX3Rlc3QucHkAamF4LnVzZXNfc2hhcGVfcG9seW1vcnBoaXNtAG1obG8ubnVtX3BhcnRpdGlvbnMAbWhsby5udW1fcmVwbGljYXMAaml0X2Z1bmMAYXJnX2F0dHJzAGZ1bmN0aW9uX3R5cGUAcmVzX2F0dHJzAHN5bV92aXNpYmlsaXR5AGFwaV92ZXJzaW9uAGJhY2tlbmRfY29uZmlnAGNhbGxfdGFyZ2V0X25hbWUAY2FsbGVkX2NvbXB1dGF0aW9ucwBoYXNfc2lkZV9lZmZlY3QAb3BlcmFuZF9sYXlvdXRzAG91dHB1dF9vcGVyYW5kX2FsaWFzZXMAcmVzdWx0X2xheW91dHMAdGYuYmFja2VuZF9jb25maWcAaml0KGZ1bmMpL2ppdChtYWluKS9jYWxsX3RmW2NhbGxhYmxlX2ZsYXRfdGY9PGZ1bmN0aW9uIGNhbGxfdGYuPGxvY2Fscz4ubWFrZV9jYWxsLjxsb2NhbHM+LmNhbGxhYmxlX2ZsYXRfdGYgYXQgMHg3ZmIwYzlhNjg0MzA+IGZ1bmN0aW9uX2ZsYXRfdGY9PGdvb2dsZTMudGhpcmRfcGFydHkudGVuc29yZmxvdy5weXRob24uZWFnZXIucG9seW1vcnBoaWNfZnVuY3Rpb24ucG9seW1vcnBoaWNfZnVuY3Rpb24uRnVuY3Rpb24gb2JqZWN0IGF0IDB4N2ZiMGM5YTU3ZDYwPiBhcmdzX2ZsYXRfc2lnX3RmPShUZW5zb3JTcGVjKHNoYXBlPSgyLCksIGR0eXBlPXRmLmZsb2F0MzIsIG5hbWU9Tm9uZSksKSBvdXRwdXRfYXZhbHM9KFNoYXBlZEFycmF5KGZsb2F0MzJbMl0pLCkgaGFzX3NpZGVfZWZmZWN0cz1UcnVlIG9yZGVyZWQ9RmFsc2UgY2FsbF90Zl9ncmFwaD1UcnVlXQBqaXQoZnVuYykvaml0KG1haW4pL2NvcwAAbWhsby5zaGFyZGluZwB7cmVwbGljYXRlZH0AamF4LnJlc3VsdF9pbmZvAG1haW4AcHVibGljAHRmLmNhbGxfdGZfZnVuY3Rpb24AY2FsbGVkX2luZGV4AGhhc190b2tlbl9pbnB1dF9vdXRwdXQAKhQKCXBsYXRmb3JtcxIHCgUSA0NQVSoNCgd2ZXJzaW9uEgIYBhpRCghJZGVudGl0eRIISWRlbnRpdHkaFlhsYUNhbGxNb2R1bGU6b3V0cHV0OjAqBwoBVBICMAEqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCGp4BCglJZGVudGl0eU4SCUlkZW50aXR5ThoWWGxhQ2FsbE1vZHVsZTpvdXRwdXQ6MBoVamF4MnRmX2FyZ18wOm91dHB1dDowKgsKAVQSBgoEMgIBASooChFfZ3JhZGllbnRfb3BfdHlwZRITEhFDdXN0b21HcmFkaWVudC0xMCogCg5fb3V0cHV0X3NoYXBlcxIOCgw6BBICCAI6BBICCAIaTwoKamF4MnRmX291dBIISWRlbnRpdHkaEklkZW50aXR5TjpvdXRwdXQ6MCoHCgFUEgIwASoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIaUAoKdGhlX3Jlc3VsdBIISWRlbnRpdHkaE2pheDJ0Zl9vdXQ6b3V0cHV0OjAqBwoBVBICMAEqGgoOX291dHB1dF9zaGFwZXMSCAoGOgQSAggCGlcKCklkZW50aXR5XzESCElkZW50aXR5GhN0aGVfcmVzdWx0Om91dHB1dDowGgVeTm9PcCoHCgFUEgIwASoaCg5fb3V0cHV0X3NoYXBlcxIICgY6BBICCAIaMgoETm9PcBIETm9PcBoOXlhsYUNhbGxNb2R1bGUqFAoOX291dHB1dF9zaGFwZXMSAgoACkoKFl9faW5mZXJlbmNlX3RmX2Z1bmNfMTkSDQoJdGhlX2lucHV0GAEaDgoKaWRlbnRpdHlfMRgBiAEBogENWGxhQ2FsbE1vZHVsZSIFCKIMEAwK1Qw4ATIHdW5rbm93bioGMi4xNC4wIgVzZXJ2ZRK4DAo4CgVDb25zdBoPCgZvdXRwdXQiBWR0eXBlIg8SBnRlbnNvcgoFdmFsdWUiDRIEdHlwZQoFZHR5cGUKLgoISWRlbnRpdHkSCgoFaW5wdXQiAVQaCwoGb3V0cHV0IgFUIgkSBHR5cGUKAVQKOQoJSWRlbnRpdHlOEgoKBWlucHV0MgFUGgsKBm91dHB1dDIBVCITMAEoARIKbGlzdCh0eXBlKQoBVAqGAQoSTWVyZ2VWMkNoZWNrcG9pbnRzEhcKE2NoZWNrcG9pbnRfcHJlZml4ZXMYBxIWChJkZXN0aW5hdGlvbl9wcmVmaXgYByIbGgIoARIEYm9vbAoPZGVsZXRlX29sZF9kaXJzIh8aAigAEgRib29sChNhbGxvd19taXNzaW5nX2ZpbGVziAEBCgYKBE5vT3AKTQoEUGFjaxIOCgZ2YWx1ZXMiAVQqAU4aCwoGb3V0cHV0IgFUIgwwASgBEgNpbnQKAU4iCRIEdHlwZQoBVCIPGgIYABIDaW50CgRheGlzCkMKC1BsYWNlaG9sZGVyGg8KBm91dHB1dCIFZHR5cGUiDRIEdHlwZQoFZHR5cGUiFBoEOgIYARIFc2hhcGUKBXNoYXBlCm8KCVJlc3RvcmVWMhIKCgZwcmVmaXgYBxIQCgx0ZW5zb3JfbmFtZXMYBxIUChBzaGFwZV9hbmRfc2xpY2VzGAcaEQoHdGVuc29yczIGZHR5cGVzIhgwASgBEgpsaXN0KHR5cGUpCgZkdHlwZXOIAQEKbAoGU2F2ZVYyEgoKBnByZWZpeBgHEhAKDHRlbnNvcl9uYW1lcxgHEhQKEHNoYXBlX2FuZF9zbGljZXMYBxIRCgd0ZW5zb3JzMgZkdHlwZXMiGDABKAESCmxpc3QodHlwZSkKBmR0eXBlc4gBAQo/CgZTZWxlY3QSDQoJY29uZGl0aW9uGAoSBgoBdCIBVBIGCgFlIgFUGgsKBm91dHB1dCIBVCIJEgR0eXBlCgFUCkgKD1NoYXJkZWRGaWxlbmFtZRIMCghiYXNlbmFtZRgHEgkKBXNoYXJkGAMSDgoKbnVtX3NoYXJkcxgDGgwKCGZpbGVuYW1lGAcKwQEKF1N0YXRlZnVsUGFydGl0aW9uZWRDYWxsEgsKBGFyZ3MyA1RpbhoOCgZvdXRwdXQyBFRvdXQiEygBEgpsaXN0KHR5cGUpCgNUaW4iFCgBEgpsaXN0KHR5cGUpCgRUb3V0IgkSBGZ1bmMKAWYiFBoCEgASBnN0cmluZwoGY29uZmlnIhoaAhIAEgZzdHJpbmcKDGNvbmZpZ19wcm90byIbGgISABIGc3RyaW5nCg1leGVjdXRvcl90eXBliAEBqAEBCkAKFFN0YXRpY1JlZ2V4RnVsbE1hdGNoEgkKBWlucHV0GAcaCgoGb3V0cHV0GAoiERIGc3RyaW5nCgdwYXR0ZXJuCkwKClN0cmluZ0pvaW4SDQoGaW5wdXRzGAcqAU4aCgoGb3V0cHV0GAciCigBEgNpbnQKAU4iFxoCEgASBnN0cmluZwoJc2VwYXJhdG9yCr8CCg1YbGFDYWxsTW9kdWxlEgsKBGFyZ3MyA1RpbhoOCgZvdXRwdXQyBFRvdXQiDhIDaW50Cgd2ZXJzaW9uIhASBnN0cmluZwoGbW9kdWxlIhUoARILbGlzdChzaGFwZSkKBFNvdXQiFCgBEgpsaXN0KHR5cGUpCgRUb3V0IhMoARIKbGlzdCh0eXBlKQoDVGluIiEaAgoAEgxsaXN0KHN0cmluZykKDWRpbV9hcmdzX3NwZWMiHRoCCgASDGxpc3Qoc3RyaW5nKQoJcGxhdGZvcm1zIh8aAgoAEgpsaXN0KGZ1bmMpCg1mdW5jdGlvbl9saXN0IiIaAigAEgRib29sChZoYXNfdG9rZW5faW5wdXRfb3V0cHV0IiMaAgoAEgxsaXN0KHN0cmluZykKD2Rpc2FibGVkX2NoZWNrc4gBAQpsCgtYbGFTaGFyZGluZxIKCgVpbnB1dCIBVBoLCgZvdXRwdXQiAVQiCRIEdHlwZQoBVCIWGgISABIGc3RyaW5nCghzaGFyZGluZyIhGgIKABIJbGlzdChpbnQpChB1bnNwZWNpZmllZF9kaW1zAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC4vLi9AUGF4SGVhZGVyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwMDAwADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwMDAzNAAwMDAwMDAwMDAwMAAwMTAyMTIAIHgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMjggbXRpbWU9MTY5MDY0MzMxMS40ODQyMDYyCgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABzYXZlZF9tb2RlbC92YXJpYWJsZXMvAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAwMDc1NQAwMTE2MjcyADAwMTE2MTAAMDAwMDAwMDAwMDAAMTQ0NjEyMjU1NTcAMDE0MjM3ACA1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHVzdGFyADAwZm9yZ2UtMDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABlbmcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC4vLi9AUGF4SGVhZGVyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwMDAwADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwMDAzNAAwMDAwMDAwMDAwMAAwMTAyMTIAIHgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMjggbXRpbWU9MTY5MDY0MzMxMS40NzkyMDYzCgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABzYXZlZF9tb2RlbC92YXJpYWJsZXMvdmFyaWFibGVzLmRhdGEtMDAwMDAtb2YtMDAwMDEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAwMDY0NAAwMTE2MjcyADAwMTE2MTAAMDAwMDAwMDAxMDQAMTQ0NjEyMjU1NTcAMDIwNTMyACAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHVzdGFyADAwZm9yZ2UtMDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABlbmcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+cHT4NChwKCBIEY2FsbAgBCg4SCnNpZ25hdHVyZXMIAioACgIqAAoXChMSD3NlcnZpbmdfZGVmYXVsdAgDKgAKAioAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALi8uL0BQYXhIZWFkZXIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMDAwMDAAMDAwMDAwMAAwMDAwMDAwADAwMDAwMDAwMDM0ADAwMDAwMDAwMDAwADAxMDIxMgAgeAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB1c3RhcgAwMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyOCBtdGltZT0xNjkwNjQzMzExLjQ4NDIwNjIKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHNhdmVkX21vZGVsL3ZhcmlhYmxlcy92YXJpYWJsZXMuaW5kZXgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwMDAwNjQ0ADAxMTYyNzIAMDAxMTYxMAAwMDAwMDAwMDIyMAAxNDQ2MTIyNTU1NwAwMTcyMzIAIDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdXN0YXIAMDBmb3JnZS0wMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGVuZwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGCAEaAggBABwLX0NIRUNLUE9JTlRBQkxFX09CSkVDVF9HUkFQSAgHEgAoRDWKUNoAAAAAAAEAAAAAjUYqnwAAAAABAAAAAMDyobAAAQJgADsAAAAAAQAAAABE9sFJQAhNDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFf7gIskdUfbAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA',
    xla_call_module_version=6,
)  # End paste
