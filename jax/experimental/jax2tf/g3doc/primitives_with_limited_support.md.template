# Primitives with limited support for jax2tf

*Last generated on (YYYY-MM-DD): {{generation_date}}*

This document summarizes known limitations of the jax2tf conversion.
There are several kinds of limitations.

  * There are some JAX primitives that are converted to TF ops that have incomplete coverage
  for data types on different kinds of devices,
  see [below](#generated-summary-of-primitives-with-unimplemented-support-in-tensorflow).

  * There are some cases when the converted program computes different results than
  the JAX program, see [below](#generated-summary-of-primitives-with-known-numerical-discrepancies-in-tensorflow).

Note that automated tests will fail if new limitations appear, but
they won't when limitations are fixed. If you see a limitation that
you think it does not exist anymore, please ask for this file to
be updated.

## Generated summary of primitives with unimplemented support in Tensorflow

The following JAX primitives are converted to Tensorflow but the result of the
conversion may trigger runtime errors when run on certain devices and with
certain data types.

This table is organized by JAX primitive, but the actual errors described
in the table are for the Tensorflow ops to which the primitive is converted to.
In general, each JAX primitive is mapped
to one Tensorflow op, e.g., `sin` is mapped to `tf.math.sin`.

The errors apply only for certain devices and compilation modes ("eager",
"graph", and "compiled"). In general, "eager" and "graph" mode share the same errors.
On TPU only the "compiled" mode is relevant.

Our priority is to ensure same coverage and numerical behavior with JAX
in the "compiled" mode, i.e., **when using XLA to compile the converted program**.
We are pretty close to that goal.

The converter has a mode in which it attempts to avoid special XLA TF ops
(`enable_xla=False`). In this mode, some primitives have additional limitations.

This table only shows errors for cases that are working in JAX (see [separate
list of unsupported or partially-supported primitives](https://github.com/google/jax/blob/main/jax/experimental/jax2tf/g3doc/jax_primitives_coverage.md) )

We do not yet have support for `pmap` (with its collective primitives),
nor for `sharded_jit` (SPMD partitioning).

We use the following abbreviations for sets of dtypes:

  * `signed` = `int8`, `int16`, `int32`, `int64`
  * `unsigned` = `uint8`, `uint16`, `uint32`, `uint64`
  * `integer` = `signed`, `unsigned`
  * `floating` = `float16`, `bfloat16`, `float32`, `float64`
  * `complex` = `complex64`, `complex128`
  * `inexact` = `floating`, `complex`
  * `all` = `integer`, `inexact`, `bool`

More detailed information can be found in the
[source code for the limitation specification](https://github.com/google/jax/blob/main/jax/experimental/jax2tf/tests/primitives_test.py).

{{tf_error_table}}

## Generated summary of primitives with known numerical discrepancies in Tensorflow

In general, we expect a JAX program to produce the same exact answer as its conversion
with jax2tf. The following table lists that cases when this does not quite hold:

{{tf_numerical_discrepancies_table}}

## Updating the documentation

To update this documentation, run the following command:

```
  JAX_ENABLE_X64=1 JAX_OUTPUT_LIMITATIONS_DOC=1 python jax/experimental/jax2tf/tests/primitives_test.py JaxPrimitiveTest.test_generate_limitations_doc
```
