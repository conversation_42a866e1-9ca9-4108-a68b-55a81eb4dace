{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "JAX2TF_getting_started.ipynb", "provenance": [], "collapsed_sections": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "mB5eSZXZIO9W"}, "source": ["JAX-TensorFlow interoperation with JAX2TF\n", "===========================================\n", "\n", "Link: go/jax2tf-colab\n", "\n", "The JAX2TF colab has been deprecated, and the example code has\n", "been moved to [jax2tf/examples](https://github.com/google/jax/tree/main/jax/experimental/jax2tf/examples). \n"]}]}