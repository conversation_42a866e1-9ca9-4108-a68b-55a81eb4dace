This directory contains a simple example of using the jax2tf converter to
produce models that can be used with TensorFlow.js. This example uses
[tfjs.converters.jax_conversion.convert_jax() API](https://github.com/tensorflow/tfjs/tree/master/tfjs-converter#calling-a-converter-function-in-python-flaxjax),
which is part of TensorFlow.JS, and uses jax2tf under the hood.

See the following blog post for more details:
[JAX on the Web with TensorFlow.js](https://blog.tensorflow.org/2022/08/jax-on-web-with-tensorflowjs.html).
