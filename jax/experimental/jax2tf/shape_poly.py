# Copyright 2021 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""This is just a backwards-compatibility stub.

The main functionality has been moved to jax.experimental.export.shape_poly
"""

# TODO(necula): Remove these stubs
from jax.experimental.export.shape_poly import (
    InconclusiveDimensionOperation,

    # PolyShape used in tensorflowjs/converters/jax_conversion.py
    PolyShape,
    # is_poly_dim is used by maths/qec.
    is_poly_dim,
)
