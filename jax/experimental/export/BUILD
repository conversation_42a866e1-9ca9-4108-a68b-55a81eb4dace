# Copyright 2023 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# JAX-export provides APIs for exporting StableHLO for serialization purposes.

load(
    "//jaxlib:jax.bzl",
    "py_deps",
)
load("@rules_python//python:defs.bzl", "py_library")

licenses(["notice"])

# Please add new users to :australis_users.
package(
    default_applicable_licenses = [],
    default_visibility = ["//visibility:private"],
)

py_library(
    name = "export",
    srcs = [
        "export.py",
        "shape_poly.py",
    ],
    srcs_version = "PY3",
    # TODO: b/255503696: enable pytype
    tags = ["pytype_unchecked_annotations"],
    visibility = ["//visibility:public"],
    deps = [
        "//jax",
    ] + py_deps("numpy"),
)
