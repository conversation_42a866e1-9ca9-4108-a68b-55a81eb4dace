# Copyright 2023 The JAX Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Contains Triton specific Pallas functions."""
try:
  from jax._src.pallas import triton
  get_compute_capability = triton.get_compute_capability
  del triton
except ImportError as e:
  raise ImportError("Cannot import Pallas Triton backend. "
                    "Make sure you've installed jax-triton.") from e
